import 'package:hive/hive.dart';

part 'lesson.g.dart';

@HiveType(typeId: 2)
class Lesson extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String groupId;

  @HiveField(2)
  DateTime dateTime;

  @HiveField(3)
  bool isCompleted;

  @HiveField(4)
  List<String> attendedStudentIds;

  @HiveField(5)
  String notes;

  @HiveField(6)
  String subject;

  @HiveField(7)
  DateTime? endTime;

  Lesson({
    required this.id,
    required this.groupId,
    required this.dateTime,
    this.isCompleted = false,
    List<String>? attendedStudentIds,
    this.subject = '',
    this.notes = '',
    this.endTime,
  }) : attendedStudentIds = attendedStudentIds ?? [];

  /// الحصول على وقت بداية الحصة
  DateTime get startTime => dateTime;
}
