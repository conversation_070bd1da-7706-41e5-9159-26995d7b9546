import 'package:flutter/material.dart';
import '../utils/device_utils.dart';

/// A collection of optimized animations for low-end devices
class OptimizedAnimations {
  /// Create a fade transition optimized for device capability
  static Widget optimizedFadeTransition({
    required Animation<double> animation,
    required Widget child,
  }) {
    // Skip animation completely on very low-end devices
    if (DeviceUtils.isLowEndDevice()) {
      return child;
    }
    
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }
  
  /// Create an optimized animated container
  static Widget optimizedAnimatedContainer({
    required Duration duration,
    required Widget child,
    Curve curve = Curves.linear,
    double? width,
    double? height,
    Color? color,
    BoxDecoration? decoration,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    // Use shorter duration on low-end devices
    final optimizedDuration = DeviceUtils.isLowEndDevice()
        ? Duration(milliseconds: duration.inMilliseconds ~/ 2)
        : duration;
    
    return AnimatedContainer(
      duration: optimizedDuration,
      curve: curve,
      width: width,
      height: height,
      color: color,
      decoration: decoration,
      padding: padding,
      margin: margin,
      child: child,
    );
  }
}