import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../providers/payment_system_provider.dart';

import '../widgets/stats_dashboard.dart';
import '../widgets/smooth_animations.dart';
import '../widgets/interactive_widgets.dart';
import '../theme/simple_theme.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Header with Smooth Animation
              SmoothAnimations.smoothEntry(
                child: InteractiveCard(
                  padding: const EdgeInsets.all(28),
                  backgroundColor: SimpleTheme.getCardColor(context),
                  child: Row(
                    children: [
                      // Enhanced Logo Container
                      Container(
                        padding: const EdgeInsets.all(14),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.white.withValues(alpha: 0.25),
                              Colors.white.withValues(alpha: 0.15),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: SimpleTheme.getBorderColor(context),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.asset(
                            'lib/logo/icon.png',
                            width: 32,
                            height: 32,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  gradient: SimpleTheme.primaryGradientReverse,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.dashboard_rounded,
                                  color: SimpleTheme.getIconColor(context),
                                  size: 20,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Enhanced Welcome Text
                            ShaderMask(
                              shaderCallback: (bounds) => LinearGradient(
                                colors: [
                                  Colors.white.withValues(alpha: 0.9),
                                  SimpleTheme.getSecondaryTextColor(context),
                                ],
                              ).createShader(bounds),
                              child: Text(
                                'مرحباً بك في',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: SimpleTheme.getTextColor(context),
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            // Enhanced Title with Gradient
                            ShaderMask(
                              shaderCallback: (bounds) => LinearGradient(
                                colors:
                                    Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? [
                                        SimpleTheme.getTextColor(context),
                                        const Color(0xFFF0F9FF),
                                      ]
                                    : [
                                        SimpleTheme.getTextColor(context),
                                        const Color(0xFFF0F9FF),
                                      ],
                              ).createShader(bounds),
                              child: Text(
                                'EduTrack',
                                style: GoogleFonts.cairo(
                                  fontSize: 26,
                                  fontWeight: FontWeight.w800,
                                  color: SimpleTheme.getTextColor(context),
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                            const SizedBox(height: 2),
                            // Subtitle
                            Text(
                              'نظام إدارة التعليم المتقدم',
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                color: SimpleTheme.getSecondaryTextColor(
                                  context,
                                ),
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Enhanced Stats Dashboard
              QuickStats(
                totalGroups: provider.totalGroups,
                totalStudents: provider.totalStudents,
                completedLessons: provider.completedLessonsToday,
                remainingLessons: provider.remainingLessonsToday,
                onGroupsTap: () => _showGroupsModal(context, provider),
                onStudentsTap: () => _showStudentsModal(context, provider),
                onCompletedTap: () =>
                    _showCompletedLessonsModal(context, provider),
                onRemainingTap: () =>
                    _showRemainingLessonsModal(context, provider),
              ),
              const SizedBox(height: 24),

              // Payment Stats
              _buildPaymentStats(),
            ],
          ),
        );
      },
    );
  }

  void _showGroupsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'المجموعات',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.groups.length,
            itemBuilder: (context, index) {
              final group = provider.groups[index];
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  group.subject,
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(Icons.groups_rounded, color: SimpleTheme.primary),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showStudentsModal(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الطلاب',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: provider.students.length,
            itemBuilder: (context, index) {
              final student = provider.students[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == student.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  student.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.person_rounded,
                  color: SimpleTheme.accentPink,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showCompletedLessonsModal(BuildContext context, AppProvider provider) {
    final completedLessons = provider
        .getTodayLessons()
        .where((l) => l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الدروس المكتملة اليوم',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: completedLessons.length,
            itemBuilder: (context, index) {
              final lesson = completedLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.check_circle_rounded,
                  color: SimpleTheme.success,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemainingLessonsModal(BuildContext context, AppProvider provider) {
    final remainingLessons = provider
        .getTodayLessons()
        .where((l) => !l.isCompleted)
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.cardBg,
        title: Text(
          'الدروس المتبقية اليوم',
          style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: remainingLessons.length,
            itemBuilder: (context, index) {
              final lesson = remainingLessons[index];
              final group = provider.groups.firstWhere(
                (g) => g.id == lesson.groupId,
                orElse: () => provider.groups.first,
              );
              return ListTile(
                title: Text(
                  group.name,
                  style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                ),
                subtitle: Text(
                  '${lesson.dateTime.hour}:${lesson.dateTime.minute.toString().padLeft(2, '0')}',
                  style: GoogleFonts.cairo(color: SimpleTheme.textSecondary),
                ),
                leading: Icon(
                  Icons.schedule_rounded,
                  color: SimpleTheme.warning,
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(color: SimpleTheme.primary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStats() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        final stats = paymentProvider.calculateStatistics();

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                const Color(0xFF6366f1).withValues(alpha: 0.1),
                const Color(0xFF8B5CF6).withValues(alpha: 0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFF6366f1).withValues(alpha: 0.2),
              width: 1.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF6366f1), Color(0xFF8B5CF6)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.payment,
                      color: SimpleTheme.getIconColor(context),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إحصائيات المدفوعات',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: SimpleTheme.getTextColor(context),
                          ),
                        ),
                        Text(
                          'متابعة المدفوعات والمستحقات',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: SimpleTheme.getSecondaryTextColor(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: _buildPaymentStatItem(
                      context,
                      'المدفوع',
                      '${stats.totalPaid.toStringAsFixed(0)} ج.م',
                      Icons.check_circle,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPaymentStatItem(
                      context,
                      'المعلق',
                      '${stats.totalPending.toStringAsFixed(0)} ج.م',
                      Icons.pending,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildPaymentStatItem(
                      context,
                      'المتأخر',
                      '${stats.totalOverdue.toStringAsFixed(0)} ج.م',
                      Icons.warning,
                      Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPaymentStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }
}
