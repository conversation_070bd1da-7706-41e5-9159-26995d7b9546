import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/simple_theme.dart';

enum BackgroundType { gradient, animated, particles, waves, geometric }

class EnhancedBackground extends StatefulWidget {
  final Widget child;
  final BackgroundType type;
  final bool enableAnimation;
  final Duration animationDuration;
  final List<Color>? customColors;
  final double intensity;

  const EnhancedBackground({
    super.key,
    required this.child,
    this.type = BackgroundType.gradient,
    this.enableAnimation = true,
    this.animationDuration = const Duration(seconds: 20),
    this.customColors,
    this.intensity = 0.1,
  });

  @override
  State<EnhancedBackground> createState() => _EnhancedBackgroundState();
}

class _EnhancedBackgroundState extends State<EnhancedBackground>
    with TickerProviderStateMixin {
  late AnimationController _primaryController;
  late AnimationController _secondaryController;
  late AnimationController _tertiaryController;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    if (widget.enableAnimation) {
      _initializeAnimations();
    }
  }

  void _initializeAnimations() {
    try {
      _primaryController = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      _secondaryController = AnimationController(
        duration: Duration(
          milliseconds: (widget.animationDuration.inMilliseconds * 1.3).round(),
        ),
        vsync: this,
      );

      _tertiaryController = AnimationController(
        duration: Duration(
          milliseconds: (widget.animationDuration.inMilliseconds * 0.8).round(),
        ),
        vsync: this,
      );

      // Start animations with delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (!_isDisposed && mounted) {
          _primaryController.repeat();
          _secondaryController.repeat();
          _tertiaryController.repeat();
        }
      });
    } catch (e) {
      // Handle initialization errors
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    if (widget.enableAnimation) {
      try {
        _primaryController.dispose();
        _secondaryController.dispose();
        _tertiaryController.dispose();
      } catch (e) {
        // Ignore disposal errors
      }
    }
    super.dispose();
  }

  Widget _buildGradientBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors:
              widget.customColors ??
              [
                SimpleTheme.darkBg,
                const Color(0xFF1E293B),
                const Color(0xFF0F172A),
              ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    if (!widget.enableAnimation || _isDisposed) {
      return _buildGradientBackground();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([
        _primaryController,
        _secondaryController,
        _tertiaryController,
      ]),
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                SimpleTheme.darkBg,
                Color.lerp(
                  const Color(0xFF1E293B),
                  SimpleTheme.primaryBlue.withValues(alpha: 0.1),
                  (math.sin(_primaryController.value * 2 * math.pi) + 1) /
                      2 *
                      widget.intensity,
                )!,
                Color.lerp(
                  const Color(0xFF0F172A),
                  SimpleTheme.primary.withValues(alpha: 0.05),
                  (math.cos(_secondaryController.value * 2 * math.pi) + 1) /
                      2 *
                      widget.intensity,
                )!,
              ],
              stops: const [0.0, 0.6, 1.0],
            ),
          ),
        );
      },
    );
  }

  Widget _buildParticlesBackground() {
    return Stack(
      children: [
        _buildGradientBackground(),
        if (widget.enableAnimation && !_isDisposed)
          RepaintBoundary(
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _primaryController,
                _secondaryController,
                _tertiaryController,
              ]),
              builder: (context, child) {
                return CustomPaint(
                  painter: ParticlesPainter(
                    animation1: _primaryController.value,
                    animation2: _secondaryController.value,
                    animation3: _tertiaryController.value,
                    intensity: widget.intensity,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildWavesBackground() {
    return Stack(
      children: [
        _buildGradientBackground(),
        if (widget.enableAnimation && !_isDisposed)
          RepaintBoundary(
            child: AnimatedBuilder(
              animation: _primaryController,
              builder: (context, child) {
                return CustomPaint(
                  painter: WavesPainter(
                    animation: _primaryController.value,
                    intensity: widget.intensity,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildGeometricBackground() {
    return Stack(
      children: [
        _buildGradientBackground(),
        if (widget.enableAnimation && !_isDisposed)
          RepaintBoundary(
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _primaryController,
                _secondaryController,
              ]),
              builder: (context, child) {
                return CustomPaint(
                  painter: GeometricPainter(
                    animation1: _primaryController.value,
                    animation2: _secondaryController.value,
                    intensity: widget.intensity,
                  ),
                  size: Size.infinite,
                );
              },
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget background;

    switch (widget.type) {
      case BackgroundType.gradient:
        background = _buildGradientBackground();
        break;
      case BackgroundType.animated:
        background = _buildAnimatedBackground();
        break;
      case BackgroundType.particles:
        background = _buildParticlesBackground();
        break;
      case BackgroundType.waves:
        background = _buildWavesBackground();
        break;
      case BackgroundType.geometric:
        background = _buildGeometricBackground();
        break;
    }

    return Stack(children: [background, widget.child]);
  }
}

// Custom Painters
class ParticlesPainter extends CustomPainter {
  final double animation1;
  final double animation2;
  final double animation3;
  final double intensity;

  ParticlesPainter({
    required this.animation1,
    required this.animation2,
    required this.animation3,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint1 = Paint()
      ..color = SimpleTheme.primaryBlue.withValues(alpha: intensity * 0.6)
      ..style = PaintingStyle.fill;

    final paint2 = Paint()
      ..color = SimpleTheme.primary.withValues(alpha: intensity * 0.4)
      ..style = PaintingStyle.fill;

    // Floating particles
    for (int i = 0; i < 8; i++) {
      final offset1 = Offset(
        size.width * (0.1 + i * 0.12) +
            math.sin((animation1 + i * 0.3) * 2 * math.pi) * 30,
        size.height * (0.2 + i * 0.1) +
            math.cos((animation1 + i * 0.2) * 2 * math.pi) * 20,
      );
      canvas.drawCircle(offset1, (4 + i * 2).toDouble(), paint1);

      final offset2 = Offset(
        size.width * (0.8 - i * 0.08) +
            math.cos((animation2 + i * 0.4) * 2 * math.pi) * 25,
        size.height * (0.3 + i * 0.08) +
            math.sin((animation2 + i * 0.3) * 2 * math.pi) * 35,
      );
      canvas.drawCircle(offset2, (3 + i).toDouble(), paint2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class WavesPainter extends CustomPainter {
  final double animation;
  final double intensity;

  WavesPainter({required this.animation, required this.intensity});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = SimpleTheme.primaryBlue.withValues(alpha: intensity * 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final path = Path();
    final waveHeight = size.height * 0.1 * intensity;

    for (double x = 0; x <= size.width; x += 5) {
      final y =
          size.height * 0.8 +
          math.sin((x / size.width + animation) * 4 * math.pi) * waveHeight;
      if (x == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GeometricPainter extends CustomPainter {
  final double animation1;
  final double animation2;
  final double intensity;

  GeometricPainter({
    required this.animation1,
    required this.animation2,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = SimpleTheme.primary.withValues(alpha: intensity * 0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // Rotating geometric shapes
    canvas.save();
    canvas.translate(size.width * 0.2, size.height * 0.3);
    canvas.rotate(animation1 * 2 * math.pi);
    canvas.drawRect(const Rect.fromLTWH(-20, -20, 40, 40), paint);
    canvas.restore();

    canvas.save();
    canvas.translate(size.width * 0.8, size.height * 0.7);
    canvas.rotate(-animation2 * 2 * math.pi);
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = i * math.pi / 3;
      final x = math.cos(angle) * 25;
      final y = math.sin(angle) * 25;
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
