import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';

class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final Color? color;
  final List<BoxShadow>? boxShadow;
  final Gradient? gradient;
  final VoidCallback? onTap;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius = 15,
    this.color,
    this.boxShadow,
    this.gradient,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardWidget = Container(
      padding: padding ?? const EdgeInsets.all(15),
      margin: margin,
      decoration: BoxDecoration(
        color: color ?? SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow:
            boxShadow ??
            [
              BoxShadow(
                color: SimpleTheme.getBorderColor(context),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
        gradient: gradient,
      ),
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius),
        child: cardWidget,
      );
    }

    return cardWidget;
  }

  // Factory constructor for creating a card with gradient
  factory ModernCard.gradient({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double borderRadius = 15,
    List<BoxShadow>? boxShadow,
    Gradient? gradient,
    VoidCallback? onTap,
  }) {
    return ModernCard(
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      boxShadow: boxShadow,
      gradient:
          gradient ??
          const LinearGradient(
            colors: [Color(0xFF6366f1), Color(0xFF8B5CF6)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
      onTap: onTap,
      child: child,
    );
  }
}
