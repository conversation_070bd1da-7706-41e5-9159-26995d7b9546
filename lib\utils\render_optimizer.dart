import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'device_utils.dart';

/// Utility class for optimizing rendering performance
class RenderOptimizer {
  /// Apply optimizations to a scrollable widget
  static Widget optimizeScrollable(Widget child) {
    // Use RepaintBoundary to prevent unnecessary repaints
    return RepaintBoundary(
      child: child,
    );
  }
  
  /// Create an optimized scrollable container
  static Widget optimizedScrollableContainer({
    required Widget child,
    ScrollController? controller,
    bool? primary,
    ScrollPhysics? physics,
    EdgeInsetsGeometry? padding,
  }) {
    return SingleChildScrollView(
      controller: controller,
      primary: primary,
      physics: physics ?? DeviceUtils.getScrollPhysics(),
      padding: padding,
      child: RepaintBoundary(child: child),
    );
  }
  
  /// Create an optimized container with performance considerations
  static Widget optimizedContainer({
    required Widget child,
    BoxDecoration? decoration,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? width,
    double? height,
    Alignment? alignment,
  }) {
    // Use cached decorations when possible
    return Container(
      decoration: decoration,
      padding: padding,
      margin: margin,
      width: width,
      height: height,
      alignment: alignment,
      child: child,
    );
  }
  
  /// Optimize animations based on device capability
  static Widget optimizedAnimatedWidget({
    required Widget child,
    required Animation<double> animation,
    bool useRepaintBoundary = true,
  }) {
    final widget = AnimatedBuilder(
      animation: animation,
      builder: (context, _) => child,
    );
    
    return useRepaintBoundary ? RepaintBoundary(child: widget) : widget;
  }
  
  /// Apply render optimizations globally
  static void applyGlobalOptimizations() {
    // Disable debug checks in release mode
    debugProfileBuildsEnabled = false;
    debugProfilePaintsEnabled = false;
    
    // Optimize for low-end devices
    if (DeviceUtils.isLowEndDevice()) {
      // Reduce render complexity
      debugDisableShadows = true;
    }
  }
  
  /// Create a widget optimized for low-end devices
  static Widget optimizedForLowEndDevices(Widget child) {
    if (!DeviceUtils.isLowEndDevice()) {
      return child;
    }
    
    // Apply optimizations for low-end devices
    return RepaintBoundary(
      child: child,
    );
  }
  
  /// Create a simplified version of a widget for low-end devices
  static Widget adaptiveWidget({
    required Widget normalWidget,
    required Widget simplifiedWidget,
  }) {
    return DeviceUtils.isLowEndDevice() ? simplifiedWidget : normalWidget;
  }
}