import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/payment_system_provider.dart';
import '../models/payment_system.dart';
import '../theme/simple_theme.dart';

class PaymentSettingsScreen extends StatefulWidget {
  const PaymentSettingsScreen({super.key});

  @override
  State<PaymentSettingsScreen> createState() => _PaymentSettingsScreenState();
}

class _PaymentSettingsScreenState extends State<PaymentSettingsScreen> {
  late PaymentSettings _settings;
  final _defaultAmountController = TextEditingController();
  final _gracePeriodController = TextEditingController();
  final _notificationDaysController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _settings = context.read<PaymentSystemProvider>().settings;
    _initializeControllers();
  }

  void _initializeControllers() {
    _defaultAmountController.text = _settings.defaultAmount.toString();
    _gracePeriodController.text = _settings.gracePeriodDays.toString();
    _notificationDaysController.text = _settings.notificationDaysBefore
        .toString();
  }

  @override
  void dispose() {
    _defaultAmountController.dispose();
    _gracePeriodController.dispose();
    _notificationDaysController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إعدادات نظام الدفع',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: Text(
              'حفظ',
              style: GoogleFonts.cairo(
                color: SimpleTheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('سياسات تحديد المتأخرين عن السداد'),
            _buildPoliciesSection(),
            const SizedBox(height: 24),

            _buildSectionTitle('منطق دمج السياسات'),
            _buildPolicyLogicSection(),
            const SizedBox(height: 24),

            _buildSectionTitle('الإعدادات العامة'),
            _buildGeneralSettings(),
            const SizedBox(height: 24),

            _buildSectionTitle('إعدادات التنبيهات'),
            _buildNotificationSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: SimpleTheme.getTextColor(context),
        ),
      ),
    );
  }

  Widget _buildPoliciesSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر طريقة أو أكثر لتحديد الطلاب المتأخرين:',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 16),

          _buildPolicyOption(
            PolicyType.sessionsCompleted,
            'عدد الحصص المكتملة',
            'إذا أكمل الطالب عدد معين من الحصص',
            Icons.school,
          ),

          _buildPolicyOption(
            PolicyType.monthlyDue,
            'الاستحقاق الشهري',
            'إذا انتهى الشهر ولم يقم بالدفع',
            Icons.calendar_month,
          ),

          _buildPolicyOption(
            PolicyType.lessonCompleted,
            'انتهاء الدرس الرئيسي',
            'إذا انتهى الدرس المحدد للمجموعة',
            Icons.task_alt,
          ),

          _buildPolicyOption(
            PolicyType.custom,
            'طريقة مخصصة',
            'تحديد مدة أو عدد حصص مخصص',
            Icons.tune,
          ),

          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _addNewPolicy,
            icon: Icon(Icons.add, color: SimpleTheme.getIconColor(context)),
            label: Text(
              'إضافة سياسة جديدة',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: SimpleTheme.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyOption(
    PolicyType type,
    String title,
    String description,
    IconData icon,
  ) {
    final existingPolicy = _settings.policies.firstWhere(
      (p) => p.type == type,
      orElse: () =>
          PaymentPolicy(id: '', name: title, type: type, isEnabled: false),
    );

    final isEnabled = existingPolicy.id.isNotEmpty && existingPolicy.isEnabled;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _togglePolicy(type, title),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isEnabled
                ? SimpleTheme.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isEnabled
                  ? SimpleTheme.primary
                  : SimpleTheme.getBorderColor(context),
              width: isEnabled ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: isEnabled
                    ? SimpleTheme.primary
                    : SimpleTheme.getSecondaryTextColor(context),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: isEnabled
                            ? SimpleTheme.primary
                            : SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      description,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Checkbox(
                value: isEnabled,
                onChanged: (value) => _togglePolicy(type, title),
                activeColor: SimpleTheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPolicyLogicSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كيفية تطبيق السياسات المختارة:',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 12),

          RadioListTile<PolicyCombination>(
            title: Text(
              'أي سياسة تنطبق (OR)',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            subtitle: Text(
              'الطالب متأخر إذا انطبقت أي من السياسات المختارة',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getSecondaryTextColor(context),
                fontSize: 12,
              ),
            ),
            value: PolicyCombination.or,
            groupValue: _settings.policyLogic,
            onChanged: (value) =>
                setState(() => _settings.policyLogic = value!),
            activeColor: SimpleTheme.primary,
          ),

          RadioListTile<PolicyCombination>(
            title: Text(
              'جميع السياسات تنطبق (AND)',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            subtitle: Text(
              'الطالب متأخر فقط إذا انطبقت جميع السياسات المختارة',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getSecondaryTextColor(context),
                fontSize: 12,
              ),
            ),
            value: PolicyCombination.and,
            groupValue: _settings.policyLogic,
            onChanged: (value) =>
                setState(() => _settings.policyLogic = value!),
            activeColor: SimpleTheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          _buildNumberField(
            'المبلغ الافتراضي (ج.م)',
            _defaultAmountController,
            'المبلغ المطلوب عند عدم تحديد رسوم المجموعة',
          ),
          const SizedBox(height: 16),

          _buildNumberField(
            'فترة السماح (أيام)',
            _gracePeriodController,
            'عدد الأيام الإضافية قبل اعتبار الدفعة متأخرة',
          ),
          const SizedBox(height: 16),

          _buildDropdownField(
            'نوع الدفع الافتراضي',
            _settings.defaultType,
            PaymentType.values,
            (value) => setState(() => _settings.defaultType = value),
            (type) => _getPaymentTypeText(type),
          ),
          const SizedBox(height: 16),

          _buildDropdownField(
            'وسيلة الدفع الافتراضية',
            _settings.defaultMethod,
            PaymentMethod.values,
            (value) => setState(() => _settings.defaultMethod = value),
            (method) => _getPaymentMethodText(method),
          ),
          const SizedBox(height: 16),

          _buildSwitchOption(
            'السماح بالسداد الجزئي',
            'إمكانية دفع جزء من المبلغ المطلوب',
            _settings.allowPartialPayments,
            (value) => setState(() => _settings.allowPartialPayments = value),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: SimpleTheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          _buildSwitchOption(
            'تفعيل التنبيهات',
            'إرسال تنبيهات للمدفوعات المستحقة والمتأخرة',
            _settings.enableNotifications,
            (value) => setState(() => _settings.enableNotifications = value),
          ),

          if (_settings.enableNotifications) ...[
            const SizedBox(height: 16),
            _buildNumberField(
              'التنبيه قبل الاستحقاق (أيام)',
              _notificationDaysController,
              'عدد الأيام قبل موعد الاستحقاق لإرسال التنبيه',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNumberField(
    String label,
    TextEditingController controller,
    String hint,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: TextInputType.number,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.cairo(
              color: SimpleTheme.getSubtitleColor(context),
              fontSize: 14,
            ),
            filled: true,
            fillColor: SimpleTheme.getBackgroundColor(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: SimpleTheme.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>(
    String label,
    T value,
    List<T> items,
    Function(T) onChanged,
    String Function(T) getText,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getBackgroundColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              isExpanded: true,
              dropdownColor: SimpleTheme.getCardColor(context),
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              items: items.map((item) {
                return DropdownMenuItem<T>(
                  value: item,
                  child: Text(getText(item)),
                );
              }).toList(),
              onChanged: (newValue) {
                if (newValue != null) onChanged(newValue);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchOption(
    String title,
    String description,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
              Text(
                description,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: SimpleTheme.primary,
        ),
      ],
    );
  }

  String _getPaymentTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.monthly:
        return 'شهرية';
      case PaymentType.perSession:
        return 'لكل حصة';
      case PaymentType.perGroup:
        return 'لكل مجموعة';
      case PaymentType.custom:
        return 'مخصصة';
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.mobileWallet:
        return 'محفظة إلكترونية';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.other:
        return 'أخرى';
    }
  }

  void _togglePolicy(PolicyType type, String name) {
    final existingIndex = _settings.policies.indexWhere((p) => p.type == type);

    if (existingIndex >= 0) {
      // تبديل حالة السياسة الموجودة
      _settings.policies[existingIndex].isEnabled =
          !_settings.policies[existingIndex].isEnabled;
    } else {
      // إضافة سياسة جديدة
      final policy = PaymentPolicy(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        type: type,
        isEnabled: true,
      );

      // إضافة معاملات افتراضية حسب النوع
      switch (type) {
        case PolicyType.sessionsCompleted:
          policy.parameters['sessions'] = 8;
          break;
        case PolicyType.custom:
          policy.parameters['days'] = 30;
          policy.parameters['sessions'] = 10;
          break;
        default:
          break;
      }

      _settings.policies.add(policy);
    }

    setState(() {});
  }

  void _addNewPolicy() {
    showDialog(
      context: context,
      builder: (context) {
        String policyName = '';
        PolicyType selectedType = PolicyType.custom;
        int sessions = 8;
        int days = 30;

        return StatefulBuilder(
          builder: (context, setDialogState) => AlertDialog(
            backgroundColor: SimpleTheme.getCardColor(context),
            title: Text(
              'إضافة سياسة مخصصة',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  decoration: InputDecoration(
                    labelText: 'اسم السياسة',
                    labelStyle: GoogleFonts.cairo(
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: SimpleTheme.primary),
                    ),
                  ),
                  onChanged: (value) => policyName = value,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<PolicyType>(
                  value: selectedType,
                  dropdownColor: SimpleTheme.getCardColor(context),
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  decoration: InputDecoration(
                    labelText: 'نوع السياسة',
                    labelStyle: GoogleFonts.cairo(
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                  ),
                  items: PolicyType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getPolicyTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) =>
                      setDialogState(() => selectedType = value!),
                ),
                if (selectedType == PolicyType.sessionsCompleted ||
                    selectedType == PolicyType.custom) ...[
                  const SizedBox(height: 16),
                  TextField(
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'عدد الحصص',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                    onChanged: (value) =>
                        sessions = int.tryParse(value) ?? sessions,
                  ),
                ],
                if (selectedType == PolicyType.custom) ...[
                  const SizedBox(height: 16),
                  TextField(
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'عدد الأيام',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                    onChanged: (value) => days = int.tryParse(value) ?? days,
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (policyName.isNotEmpty) {
                    final policy = PaymentPolicy(
                      id: DateTime.now().millisecondsSinceEpoch.toString(),
                      name: policyName,
                      type: selectedType,
                      isEnabled: true,
                    );

                    if (selectedType == PolicyType.sessionsCompleted ||
                        selectedType == PolicyType.custom) {
                      policy.parameters['sessions'] = sessions;
                    }
                    if (selectedType == PolicyType.custom) {
                      policy.parameters['days'] = days;
                    }

                    setState(() => _settings.policies.add(policy));
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: SimpleTheme.primary,
                ),
                child: Text(
                  'إضافة',
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getPolicyTypeText(PolicyType type) {
    switch (type) {
      case PolicyType.sessionsCompleted:
        return 'عدد الحصص';
      case PolicyType.monthlyDue:
        return 'استحقاق شهري';
      case PolicyType.lessonCompleted:
        return 'انتهاء الدرس';
      case PolicyType.custom:
        return 'مخصص';
    }
  }

  void _saveSettings() {
    // تحديث القيم من الحقول
    _settings.defaultAmount =
        double.tryParse(_defaultAmountController.text) ??
        _settings.defaultAmount;
    _settings.gracePeriodDays =
        int.tryParse(_gracePeriodController.text) ?? _settings.gracePeriodDays;
    _settings.notificationDaysBefore =
        int.tryParse(_notificationDaysController.text) ??
        _settings.notificationDaysBefore;

    // حفظ الإعدادات
    context.read<PaymentSystemProvider>().updateSettings(_settings);

    // إظهار رسالة نجاح
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حفظ الإعدادات بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: Colors.green,
      ),
    );

    Navigator.pop(context);
  }
}
