import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import '../models/payment_system.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../models/lesson.dart';

class PaymentSystemProvider extends ChangeNotifier {
  Box<Payment>? _paymentsBox;
  Box<PaymentSettings>? _settingsBox;
  PaymentSettings? _settings;

  List<Payment> _payments = [];
  List<Payment> get payments => _payments;

  PaymentSettings get settings =>
      _settings ?? PaymentSettings(createdAt: DateTime.now());

  /// تهيئة المزود
  Future<void> initialize() async {
    try {
      // التأكد من أن الصناديق مفتوحة
      if (Hive.isBoxOpen('payments')) {
        _paymentsBox = Hive.box<Payment>('payments');
      } else {
        _paymentsBox = await Hive.openBox<Payment>('payments');
      }

      if (Hive.isBoxOpen('payment_settings')) {
        _settingsBox = Hive.box<PaymentSettings>('payment_settings');
      } else {
        _settingsBox = await Hive.openBox<PaymentSettings>('payment_settings');
      }

      _loadPayments();
      _loadSettings();

      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing PaymentSystemProvider: $e');
      // إنشاء إعدادات افتراضية في حالة الخطأ
      _settings = PaymentSettings.defaultSettings();
      _payments = [];
    }
  }

  /// تحميل المدفوعات
  void _loadPayments() {
    if (_paymentsBox != null) {
      _payments = _paymentsBox!.values.toList();
      _updateOverduePayments();
    }
  }

  /// تحميل الإعدادات
  void _loadSettings() {
    if (_settingsBox != null && _settingsBox!.isNotEmpty) {
      _settings = _settingsBox!.values.first;
    } else {
      _settings = PaymentSettings(createdAt: DateTime.now());
      _saveSettings();
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    if (_settingsBox != null && _settings != null) {
      await _settingsBox!.clear();
      await _settingsBox!.add(_settings!);
    }
  }

  /// تحديث إعدادات الدفع
  Future<void> updateSettings(PaymentSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    notifyListeners();
  }

  /// إنشاء دفعة جديدة
  Future<void> createPayment({
    required String studentId,
    required String groupId,
    required double totalAmount,
    required PaymentType type,
    required DateTime dueDate,
    PaymentMethod method = PaymentMethod.cash,
    String? notes,
  }) async {
    if (_paymentsBox == null) return;

    final payment = Payment(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      studentId: studentId,
      groupId: groupId,
      totalAmount: totalAmount,
      type: type,
      method: method,
      dueDate: dueDate,
      notes: notes,
      createdAt: DateTime.now(),
    );

    await _paymentsBox!.add(payment);

    // إعادة تحميل البيانات وتحديث الحالات
    _loadPayments();

    // التأكد من تحديث الحالات المتأخرة
    _updateOverduePayments();

    notifyListeners();
  }

  /// إضافة معاملة دفع (للسداد الجزئي)
  Future<void> addPaymentTransaction({
    required String paymentId,
    required double amount,
    required PaymentMethod method,
    String? notes,
    String? receiptNumber,
  }) async {
    final paymentIndex = _payments.indexWhere((p) => p.id == paymentId);
    if (paymentIndex == -1) {
      throw Exception('Payment not found: $paymentId');
    }

    final payment = _payments[paymentIndex];

    final transaction = PaymentTransaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      amount: amount,
      method: method,
      date: DateTime.now(),
      notes: notes,
      receiptNumber: receiptNumber,
    );

    payment.addTransaction(transaction);

    // حفظ التحديث في قاعدة البيانات
    await _paymentsBox?.put(payment.key, payment);
    _loadPayments();
    notifyListeners();
  }

  /// تسجيل دفعة كاملة
  Future<void> markPaymentAsPaid({
    required String paymentId,
    PaymentMethod? method,
    String? notes,
    String? receiptNumber,
  }) async {
    final paymentIndex = _payments.indexWhere((p) => p.id == paymentId);
    if (paymentIndex == -1) {
      throw Exception('Payment not found: $paymentId');
    }

    final payment = _payments[paymentIndex];
    final remainingAmount = payment.remainingAmount;

    if (remainingAmount > 0) {
      await addPaymentTransaction(
        paymentId: paymentId,
        amount: remainingAmount,
        method: method ?? payment.method,
        notes: notes,
        receiptNumber: receiptNumber,
      );
    }
  }

  /// إلغاء دفعة
  Future<void> cancelPayment(String paymentId, {String? reason}) async {
    final paymentIndex = _payments.indexWhere((p) => p.id == paymentId);
    if (paymentIndex == -1) {
      throw Exception('Payment not found: $paymentId');
    }

    final payment = _payments[paymentIndex];
    payment.updateStatus(PaymentStatus.cancelled);
    if (reason != null) {
      payment.notes = '${payment.notes ?? ''}\nملغية: $reason';
    }

    // حفظ التحديث
    await _paymentsBox?.put(payment.key, payment);
    _loadPayments();
    notifyListeners();
  }

  /// حذف دفعة
  Future<void> deletePayment(String paymentId) async {
    final payment = _payments.firstWhere((p) => p.id == paymentId);
    await payment.delete();
    _loadPayments();
    notifyListeners();
  }

  /// تحديث المدفوعات المتأخرة
  void _updateOverduePayments() {
    bool hasChanges = false;
    for (final payment in _payments) {
      if (payment.status == PaymentStatus.pending && payment.isOverdue) {
        payment.updateStatus(PaymentStatus.overdue);
        hasChanges = true;
      }
    }

    // حفظ التغييرات إذا كانت موجودة
    if (hasChanges && _paymentsBox != null) {
      for (final payment in _payments) {
        if (payment.status == PaymentStatus.overdue) {
          _paymentsBox!.put(payment.key, payment);
        }
      }
    }
  }

  /// الحصول على مدفوعات طالب معين
  List<Payment> getStudentPayments(String studentId) {
    return _payments.where((p) => p.studentId == studentId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// الحصول على مدفوعات مجموعة معينة
  List<Payment> getGroupPayments(String groupId) {
    return _payments.where((p) => p.groupId == groupId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// الحصول على المدفوعات المتأخرة
  List<Payment> get overduePayments {
    return _payments.where((p) => p.status == PaymentStatus.overdue).toList()
      ..sort((a, b) => b.daysOverdue.compareTo(a.daysOverdue));
  }

  /// الحصول على المدفوعات المعلقة
  List<Payment> get pendingPayments {
    return _payments.where((p) => p.status == PaymentStatus.pending).toList()
      ..sort((a, b) => a.dueDate.compareTo(b.dueDate));
  }

  /// الحصول على المدفوعات المدفوعة جزئياً
  List<Payment> get partialPayments {
    return _payments.where((p) => p.status == PaymentStatus.partial).toList()
      ..sort(
        (a, b) => b.updatedAt?.compareTo(a.updatedAt ?? DateTime.now()) ?? 0,
      );
  }

  /// حساب إحصائيات المدفوعات
  PaymentStatistics calculateStatistics() {
    // إذا لم توجد دفعات، إرجاع إحصائيات فارغة
    if (_payments.isEmpty) {
      return PaymentStatistics(
        totalPaid: 0.0,
        totalPending: 0.0,
        totalOverdue: 0.0,
        totalPartial: 0.0,
        studentsCount: 0,
        paidStudentsCount: 0,
        overdueStudentsCount: 0,
        partialStudentsCount: 0,
        revenueByGroup: {},
        revenueByMonth: {},
      );
    }

    final pendingPayments = _payments.where(
      (p) => p.status == PaymentStatus.pending,
    );
    final overduePayments = _payments.where(
      (p) => p.status == PaymentStatus.overdue,
    );
    final partialPayments = _payments.where(
      (p) => p.status == PaymentStatus.partial,
    );

    // حساب الإيرادات حسب المجموعة
    final revenueByGroup = <String, double>{};
    for (final payment in _payments) {
      if (payment.paidAmount > 0) {
        revenueByGroup[payment.groupId] =
            (revenueByGroup[payment.groupId] ?? 0) + payment.paidAmount;
      }
    }

    // حساب الإيرادات حسب الشهر
    final revenueByMonth = <String, double>{};
    for (final payment in _payments) {
      if (payment.paidAmount > 0) {
        final date = payment.paidDate ?? payment.createdAt;
        final monthKey =
            '${date.year}-${date.month.toString().padLeft(2, '0')}';
        revenueByMonth[monthKey] =
            (revenueByMonth[monthKey] ?? 0) + payment.paidAmount;
      }
    }

    // حساب عدد الطلاب بناءً على الحالات الفعلية
    final allStudentIds = _payments.map((p) => p.studentId).toSet();
    final paidStudentIds = <String>{};
    final overdueStudentIds = <String>{};
    final partialStudentIds = <String>{};

    // تجميع الطلاب حسب حالاتهم الفعلية
    for (final studentId in allStudentIds) {
      final studentPayments = _payments.where((p) => p.studentId == studentId);
      final totalPaid = studentPayments.fold(
        0.0,
        (sum, p) => sum + p.paidAmount,
      );
      final totalRemaining = studentPayments.fold(
        0.0,
        (sum, p) => sum + p.remainingAmount,
      );
      final hasOverdue = studentPayments.any(
        (p) => p.status == PaymentStatus.overdue,
      );

      if (hasOverdue) {
        overdueStudentIds.add(studentId);
      } else if (totalPaid > 0 && totalRemaining > 0) {
        partialStudentIds.add(studentId);
      } else if (totalPaid > 0 && totalRemaining == 0) {
        paidStudentIds.add(studentId);
      }
      // الطلاب الذين لم يدفعوا شيئاً لا يُحسبون كمدفوعين
    }

    final totalPaid = _payments.fold(0.0, (sum, p) => sum + p.paidAmount);
    final totalPending =
        pendingPayments.fold(0.0, (sum, p) => sum + p.remainingAmount) +
        partialPayments.fold(0.0, (sum, p) => sum + p.remainingAmount);
    final totalOverdue = overduePayments.fold(
      0.0,
      (sum, p) => sum + p.remainingAmount,
    );

    return PaymentStatistics(
      totalPaid: totalPaid,
      totalPending: totalPending,
      totalOverdue: totalOverdue,
      totalPartial: partialPayments.fold(0.0, (sum, p) => sum + p.paidAmount),
      studentsCount: allStudentIds.length,
      paidStudentsCount: paidStudentIds.length,
      overdueStudentsCount: overdueStudentIds.length,
      partialStudentsCount: partialStudentIds.length,
      revenueByGroup: revenueByGroup,
      revenueByMonth: revenueByMonth,
    );
  }

  /// الحصول على معلومات دفعات طالب
  StudentPaymentInfo getStudentPaymentInfo(
    String studentId,
    String studentName,
    String groupName,
  ) {
    final studentPayments = getStudentPayments(studentId);

    // إذا لم توجد دفعات، الطالب لم يسدد شيئاً
    if (studentPayments.isEmpty) {
      return StudentPaymentInfo(
        studentId: studentId,
        studentName: studentName,
        groupName: groupName,
        payments: [],
        totalPaid: 0.0,
        totalPending: 0.0,
        totalOverdue: 0.0,
        overallStatus: PaymentStatus.pending,
        overdueReasons: ['لم يتم إنشاء أي دفعات بعد'],
      );
    }

    final paidAmount = studentPayments.fold(
      0.0,
      (sum, p) => sum + p.paidAmount,
    );
    final pendingAmount = studentPayments
        .where((p) => p.status == PaymentStatus.pending)
        .fold(0.0, (sum, p) => sum + p.remainingAmount);
    final overdueAmount = studentPayments
        .where((p) => p.status == PaymentStatus.overdue)
        .fold(0.0, (sum, p) => sum + p.remainingAmount);
    final partialAmount = studentPayments
        .where((p) => p.status == PaymentStatus.partial)
        .fold(0.0, (sum, p) => sum + p.remainingAmount);

    // تحديد الحالة العامة بناءً على المبالغ الفعلية
    PaymentStatus overallStatus;
    if (overdueAmount > 0) {
      overallStatus = PaymentStatus.overdue;
    } else if (partialAmount > 0) {
      overallStatus = PaymentStatus.partial;
    } else if (pendingAmount > 0) {
      overallStatus = PaymentStatus.pending;
    } else if (paidAmount > 0) {
      overallStatus = PaymentStatus.paid;
    } else {
      overallStatus = PaymentStatus.pending;
    }

    // أسباب التأخير
    final overdueReasons = <String>[];
    for (final payment in studentPayments.where(
      (p) => p.status == PaymentStatus.overdue,
    )) {
      overdueReasons.add(
        'دفعة ${_getPaymentTypeText(payment.type)} متأخرة ${payment.daysOverdue} يوم',
      );
    }

    // إضافة المبالغ الجزئية للمعلق
    final totalPendingWithPartial = pendingAmount + partialAmount;

    return StudentPaymentInfo(
      studentId: studentId,
      studentName: studentName,
      groupName: groupName,
      payments: studentPayments,
      totalPaid: paidAmount,
      totalPending: totalPendingWithPartial,
      totalOverdue: overdueAmount,
      overallStatus: overallStatus,
      overdueReasons: overdueReasons,
    );
  }

  String _getPaymentTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.monthly:
        return 'شهرية';
      case PaymentType.perSession:
        return 'لكل حصة';
      case PaymentType.perGroup:
        return 'لكل مجموعة';
      case PaymentType.custom:
        return 'مخصصة';
    }
  }

  /// تحليل سياسات الدفع لطالب معين
  List<String> analyzeStudentPaymentPolicies(
    String studentId,
    List<Student> students,
    List<Group> groups,
    List<Lesson> lessons,
  ) {
    final student = students.firstWhere((s) => s.id == studentId);
    final group = groups.firstWhere((g) => g.id == student.groupId);
    final studentLessons = lessons.where((l) => l.groupId == group.id).toList();
    final attendedLessons = studentLessons
        .where((l) => l.attendedStudentIds.contains(studentId))
        .toList();

    final violations = <String>[];

    for (final policy in settings.policies.where((p) => p.isEnabled)) {
      switch (policy.type) {
        case PolicyType.sessionsCompleted:
          final requiredSessions = policy.parameters['sessions'] as int? ?? 8;
          if (attendedLessons.length >= requiredSessions) {
            violations.add(
              'أكمل ${attendedLessons.length} حصص (المطلوب: $requiredSessions)',
            );
          }
          break;

        case PolicyType.monthlyDue:
          final now = DateTime.now();
          final monthStart = DateTime(now.year, now.month, 1);
          if (now.isAfter(monthStart.add(const Duration(days: 30)))) {
            violations.add('انتهى الشهر ولم يتم السداد');
          }
          break;

        case PolicyType.lessonCompleted:
          final lessonName = policy.parameters['lessonName'] as String?;
          final lessonDate = policy.parameters['lessonDate'] as DateTime?;

          if (lessonName != null) {
            final targetLesson = studentLessons
                .where((l) => l.subject.contains(lessonName))
                .firstOrNull;
            if (targetLesson != null && targetLesson.isCompleted) {
              violations.add('انتهى الدرس الرئيسي: $lessonName');
            }
          }

          if (lessonDate != null && DateTime.now().isAfter(lessonDate)) {
            violations.add('تجاوز تاريخ الدرس المحدد');
          }
          break;

        case PolicyType.custom:
          final customDays = policy.parameters['days'] as int?;
          final customSessions = policy.parameters['sessions'] as int?;

          if (customDays != null) {
            final startDate = student.lastAttendance;
            if (DateTime.now().difference(startDate).inDays >= customDays) {
              violations.add('تجاوز المدة المخصصة: $customDays يوم');
            }
          }

          if (customSessions != null &&
              attendedLessons.length >= customSessions) {
            violations.add('أكمل العدد المخصص: $customSessions حصة');
          }
          break;
      }
    }

    return violations;
  }

  /// فحص جميع الطلاب وتحديد المتأخرين
  List<StudentPaymentInfo> getOverdueStudents(
    List<Student> students,
    List<Group> groups,
    List<Lesson> lessons,
  ) {
    final overdueStudents = <StudentPaymentInfo>[];

    for (final student in students) {
      final group = groups.firstWhere((g) => g.id == student.groupId);
      final violations = analyzeStudentPaymentPolicies(
        student.id,
        students,
        groups,
        lessons,
      );

      if (violations.isNotEmpty) {
        final paymentInfo = getStudentPaymentInfo(
          student.id,
          student.name,
          group.name,
        );

        // إضافة انتهاكات السياسات إلى أسباب التأخير
        final allReasons = [...paymentInfo.overdueReasons, ...violations];

        final updatedInfo = StudentPaymentInfo(
          studentId: paymentInfo.studentId,
          studentName: paymentInfo.studentName,
          groupName: paymentInfo.groupName,
          payments: paymentInfo.payments,
          totalPaid: paymentInfo.totalPaid,
          totalPending: paymentInfo.totalPending,
          totalOverdue: paymentInfo.totalOverdue,
          overallStatus: PaymentStatus.overdue,
          overdueReasons: allReasons,
        );

        overdueStudents.add(updatedInfo);
      }
    }

    return overdueStudents;
  }

  /// تطبيق السياسات وإنشاء دفعات تلقائية
  Future<void> applyPoliciesAndCreatePayments(
    List<Student> students,
    List<Group> groups,
    List<Lesson> lessons,
  ) async {
    final overdueStudents = getOverdueStudents(students, groups, lessons);

    for (final studentInfo in overdueStudents) {
      // البحث عن الطالب بأمان
      final studentIndex = students.indexWhere(
        (s) => s.id == studentInfo.studentId,
      );
      if (studentIndex == -1) continue;

      final student = students[studentIndex];

      // البحث عن المجموعة بأمان
      final groupIndex = groups.indexWhere((g) => g.id == student.groupId);
      if (groupIndex == -1) continue;

      final group = groups[groupIndex];

      // التحقق من عدم وجود دفعة معلقة أو متأخرة أو جزئية بالفعل
      final existingPayment = getStudentPayments(student.id)
          .where(
            (p) =>
                p.status == PaymentStatus.pending ||
                p.status == PaymentStatus.overdue ||
                p.status == PaymentStatus.partial,
          )
          .firstOrNull;

      if (existingPayment == null) {
        // إنشاء دفعة جديدة
        final amount = group.monthlyFee > 0
            ? group.monthlyFee
            : settings.defaultAmount;
        final dueDate = DateTime.now().add(
          Duration(days: settings.gracePeriodDays),
        );

        await createPayment(
          studentId: student.id,
          groupId: group.id,
          totalAmount: amount,
          type: settings.defaultType,
          dueDate: dueDate,
          notes: 'دفعة تلقائية - ${studentInfo.overdueReasons.join(', ')}',
        );
      }
    }
  }

  @override
  void dispose() {
    _paymentsBox?.close();
    _settingsBox?.close();
    super.dispose();
  }
}
