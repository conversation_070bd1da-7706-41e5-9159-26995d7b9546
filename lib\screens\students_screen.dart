import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../theme/simple_theme.dart';

class StudentsScreen extends StatefulWidget {
  const StudentsScreen({super.key});

  @override
  State<StudentsScreen> createState() => _StudentsScreenState();
}

class _StudentsScreenState extends State<StudentsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: SimpleTheme.getBackgroundGradient(context),
            ),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    _buildHeader(context, provider),
                    const SizedBox(height: 24),

                    // Stats Cards
                    _buildStatsCards(provider),
                    const SizedBox(height: 24),

                    // Search Section
                    _buildSearch(provider),
                    const SizedBox(height: 20),

                    // Students List
                    _buildStudentsList(provider),

                    // إضافة مساحة إضافية في الأسفل
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء الهيدر
  Widget _buildHeader(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // العنوان والوصف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                    shadows: [
                      Shadow(
                        color: SimpleTheme.getContainerColor(context),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'عرض وإدارة جميع الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقات الإحصائيات البسيطة
  Widget _buildStatsCards(AppProvider provider) {
    final allStudents = provider.getAllStudents();
    final presentStudents = allStudents.where((s) => s.isPresent).length;
    final paidStudents = allStudents.where((s) => s.hasPaid).length;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSimpleStatCard(
              'الطلاب',
              allStudents.length.toString(),
              Icons.people_rounded,
              const Color(0xFF6366f1),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: SimpleTheme.getBorderColor(context),
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          Expanded(
            child: _buildSimpleStatCard(
              'الحاضرون',
              presentStudents.toString(),
              Icons.check_circle_rounded,
              const Color(0xFF10b981),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: SimpleTheme.getBorderColor(context),
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          Expanded(
            child: _buildSimpleStatCard(
              'المدفوعون',
              paidStudents.toString(),
              Icons.payment_rounded,
              const Color(0xFFf59e0b),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 8),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: SimpleTheme.getSecondaryTextColor(context),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // بناء البحث
  Widget _buildSearch(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: SimpleTheme.getBorderColor(context)),
      ),
      child: TextField(
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          hintText: 'البحث عن طالب...',
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSubtitleColor(context),
          ),
          border: InputBorder.none,
          icon: Icon(Icons.search, color: SimpleTheme.getSecondaryTextColor(context)),
        ),
        onChanged: (value) => setState(() => _searchQuery = value),
      ),
    );
  }

  // بناء قائمة الطلاب
  Widget _buildStudentsList(AppProvider provider) {
    final allStudents = provider.getAllStudents();
    var filteredStudents = allStudents.where((student) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          student.name.toLowerCase().contains(_searchQuery.toLowerCase());
      return matchesSearch;
    }).toList();

    if (filteredStudents.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'قائمة الطلاب (${filteredStudents.length})',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final student = filteredStudents[index];
            final group = provider.groups.firstWhere(
              (g) => g.id == student.groupId,
              orElse: () => provider.groups.first,
            );
            return _buildStudentCard(student, group, provider);
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: SimpleTheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.people_rounded,
              size: 64,
              color: SimpleTheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد طلاب بعد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الزر أدناه لإضافة طالب جديد',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  // Placeholder methods - will be implemented

  void _showEditStudentDialog(
    BuildContext context,
    AppProvider provider,
    student,
  ) {
    // TODO: Implement edit student dialog
  }

  void _showNotesDialog(BuildContext context, AppProvider provider, student) {
    // TODO: Implement notes dialog
  }

  void _showDeleteConfirmation(
    BuildContext context,
    AppProvider provider,
    student,
  ) {
    // TODO: Implement delete confirmation
  }

  Widget _buildStudentCard(student, group, AppProvider provider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: SimpleTheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.primary.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: SimpleTheme.primary.withValues(alpha: 0.1),
          child: Icon(Icons.person_rounded, color: SimpleTheme.primary),
        ),
        title: Text(
          student.name,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'المجموعة: ${group.name}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
            if (student.phoneNumber != null && student.phoneNumber!.isNotEmpty)
              Text(
                'الهاتف: ${student.phoneNumber}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
            if (student.notes != null && student.notes!.isNotEmpty)
              Text(
                'ملاحظات: ${student.notes}',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditStudentDialog(context, provider, student);
                break;
              case 'notes':
                _showNotesDialog(context, provider, student);
                break;
              case 'delete':
                _showDeleteConfirmation(context, provider, student);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(
                    Icons.edit_rounded,
                    size: 18,
                    color: SimpleTheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text('تعديل', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'notes',
              child: Row(
                children: [
                  Icon(
                    Icons.note_add_rounded,
                    size: 18,
                    color: SimpleTheme.accent,
                  ),
                  const SizedBox(width: 8),
                  Text('الملاحظات', style: GoogleFonts.cairo()),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                  const SizedBox(width: 8),
                  Text('حذف', style: GoogleFonts.cairo()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
