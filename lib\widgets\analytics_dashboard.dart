import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';
import '../widgets/simple_background.dart';

/// لوحة التحليلات المتقدمة
class AnalyticsDashboard extends StatelessWidget {
  final Map<String, dynamic> analyticsData;

  const AnalyticsDashboard({super.key, required this.analyticsData});

  @override
  Widget build(BuildContext context) {
    return SimpleBackground(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني للحضور
            _buildAttendanceChart(),

            const SizedBox(height: 24),

            // مؤشرات الأداء الرئيسية
            _buildKPICards(),

            const SizedBox(height: 24),

            // رسم بياني للأداء الشهري
            _buildMonthlyPerformanceChart(),

            const SizedBox(height: 24),

            // إحصائيات المجموعات
            _buildGroupStatistics(),
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceChart() {
    return SimpleCardBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معدل الحضور الأسبوعي',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // رسم بياني مبسط
          SizedBox(
            height: 200,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildChartBar('السبت', 85, SimpleTheme.primary),
                _buildChartBar('الأحد', 92, SimpleTheme.secondary),
                _buildChartBar('الاثنين', 78, SimpleTheme.accent),
                _buildChartBar('الثلاثاء', 95, SimpleTheme.primary),
                _buildChartBar('الأربعاء', 88, SimpleTheme.secondary),
                _buildChartBar('الخميس', 90, SimpleTheme.accent),
                _buildChartBar('الجمعة', 82, SimpleTheme.primary),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartBar(String day, int percentage, Color color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '$percentage%',
          style: GoogleFonts.cairo(fontSize: 12, color: SimpleTheme.textMuted),
        ),
        const SizedBox(height: 4),
        Container(
          width: 30,
          height: (percentage / 100) * 150,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [color, color.withValues(alpha: 0.7)],
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          day,
          style: GoogleFonts.cairo(fontSize: 10, color: SimpleTheme.textMuted),
        ),
      ],
    );
  }

  Widget _buildKPICards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مؤشرات الأداء الرئيسية',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                'معدل الحضور',
                '87%',
                Icons.people,
                SimpleTheme.primary,
                '+5%',
                true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'الطلاب النشطون',
                '156',
                Icons.school,
                SimpleTheme.secondary,
                '+12',
                true,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildKPICard(
                'الحصص المكتملة',
                '24',
                Icons.check_circle,
                SimpleTheme.accent,
                '+3',
                true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildKPICard(
                'متوسط الدرجات',
                '8.5',
                Icons.star,
                SimpleTheme.primary,
                '+0.3',
                true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKPICard(
    String title,
    String value,
    IconData icon,
    Color color,
    String change,
    bool isPositive,
  ) {
    return SimpleCardBackground(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isPositive
                      ? Colors.green.withValues(alpha: 0.2)
                      : Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  change,
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: isPositive ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: SimpleTheme.textPrimary,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.textMuted,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyPerformanceChart() {
    return SimpleCardBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأداء الشهري',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // خط بياني مبسط
          SizedBox(
            height: 150,
            child: CustomPaint(
              painter: LineChartPainter(),
              size: const Size(double.infinity, 150),
            ),
          ),

          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem('الحضور', SimpleTheme.primary),
              _buildLegendItem('الأداء', SimpleTheme.secondary),
              _buildLegendItem('المشاركة', SimpleTheme.accent),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.cairo(fontSize: 12, color: SimpleTheme.textMuted),
        ),
      ],
    );
  }

  Widget _buildGroupStatistics() {
    return SimpleCardBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات المجموعات',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          _buildGroupStatItem('مجموعة الرياضيات', 85, 25, SimpleTheme.primary),
          _buildGroupStatItem('مجموعة الفيزياء', 92, 18, SimpleTheme.secondary),
          _buildGroupStatItem('مجموعة الكيمياء', 78, 22, SimpleTheme.accent),
          _buildGroupStatItem('مجموعة الأحياء', 88, 20, SimpleTheme.primary),
        ],
      ),
    );
  }

  Widget _buildGroupStatItem(
    String groupName,
    int attendance,
    int students,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  groupName,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: SimpleTheme.textPrimary,
                  ),
                ),
                Text(
                  '$students طالب • معدل الحضور $attendance%',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: SimpleTheme.textMuted,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 60,
            height: 6,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: attendance / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// رسام الخط البياني المخصص
class LineChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = SimpleTheme.primary
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();

    // نقاط البيانات التجريبية
    final points = [
      Offset(0, size.height * 0.7),
      Offset(size.width * 0.2, size.height * 0.5),
      Offset(size.width * 0.4, size.height * 0.3),
      Offset(size.width * 0.6, size.height * 0.4),
      Offset(size.width * 0.8, size.height * 0.2),
      Offset(size.width, size.height * 0.1),
    ];

    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    canvas.drawPath(path, paint);

    // رسم النقاط
    final pointPaint = Paint()
      ..color = SimpleTheme.primary
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
