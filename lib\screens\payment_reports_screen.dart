import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/payment_system_provider.dart';
import '../providers/app_provider.dart';
import '../models/payment_system.dart';
import '../theme/simple_theme.dart';

class PaymentReportsScreen extends StatefulWidget {
  const PaymentReportsScreen({super.key});

  @override
  State<PaymentReportsScreen> createState() => _PaymentReportsScreenState();
}

class _PaymentReportsScreenState extends State<PaymentReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedGroup = 'الكل';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'تقارير المدفوعات',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            onPressed: _exportReport,
            icon: Icon(
              Icons.download,
              color: SimpleTheme.getIconColor(context),
            ),
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilters(),
          _buildTabBar(),
          Expanded(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDateFilter(
                  'من',
                  _startDate,
                  (date) => setState(() => _startDate = date),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDateFilter(
                  'إلى',
                  _endDate,
                  (date) => setState(() => _endDate = date),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildGroupFilter(),
        ],
      ),
    );
  }

  Widget _buildDateFilter(
    String label,
    DateTime date,
    Function(DateTime) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDate(date, onChanged),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: SimpleTheme.getCardColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: SimpleTheme.getBorderColor(context)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: SimpleTheme.primary,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${date.day}/${date.month}/${date.year}',
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupFilter() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final groups = ['الكل', ...appProvider.groups.map((g) => g.name)];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المجموعة',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: SimpleTheme.getCardColor(context),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: SimpleTheme.getBorderColor(context)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedGroup,
                  isExpanded: true,
                  dropdownColor: SimpleTheme.getCardColor(context),
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  items: groups.map((group) {
                    return DropdownMenuItem<String>(
                      value: group,
                      child: Text(group),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedGroup = value!),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: SimpleTheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: SimpleTheme.getTextColor(context),
        unselectedLabelColor: SimpleTheme.getSecondaryTextColor(context),
        labelStyle: GoogleFonts.cairo(
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'الطلاب المتأخرين'),
          Tab(text: 'الإيرادات'),
          Tab(text: 'الإحصائيات'),
          Tab(text: 'التفاصيل'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverdueStudentsReport(),
        _buildRevenueReport(),
        _buildStatisticsReport(),
        _buildDetailedReport(),
      ],
    );
  }

  Widget _buildOverdueStudentsReport() {
    return Consumer2<AppProvider, PaymentSystemProvider>(
      builder: (context, appProvider, paymentProvider, child) {
        final overdueStudents = paymentProvider.getOverdueStudents(
          appProvider.students,
          appProvider.groups,
          appProvider.lessons,
        );

        // تطبيق فلتر المجموعة
        final filteredStudents = _selectedGroup == 'الكل'
            ? overdueStudents
            : overdueStudents
                  .where((s) => s.groupName == _selectedGroup)
                  .toList();

        if (filteredStudents.isEmpty) {
          return _buildEmptyState(
            'لا توجد طلاب متأخرين',
            'جميع الطلاب محدثين في مدفوعاتهم',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final studentInfo = filteredStudents[index];
            return _buildOverdueStudentCard(studentInfo);
          },
        );
      },
    );
  }

  Widget _buildRevenueReport() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        final stats = paymentProvider.calculateStatistics();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildRevenueCard(
                'إجمالي الإيرادات',
                stats.totalPaid,
                Colors.green,
                Icons.trending_up,
              ),
              const SizedBox(height: 16),
              _buildRevenueCard(
                'المبالغ المعلقة',
                stats.totalPending,
                Colors.orange,
                Icons.pending,
              ),
              const SizedBox(height: 16),
              _buildRevenueCard(
                'المبالغ المتأخرة',
                stats.totalOverdue,
                Colors.red,
                Icons.warning,
              ),
              const SizedBox(height: 24),

              _buildSectionTitle('الإيرادات حسب المجموعة'),
              ...stats.revenueByGroup.entries.map((entry) {
                final groupName = _getGroupName(entry.key);
                return _buildRevenueItem(groupName, entry.value);
              }),

              const SizedBox(height: 24),
              _buildSectionTitle('الإيرادات حسب الشهر'),
              ...stats.revenueByMonth.entries.map((entry) {
                return _buildRevenueItem(_formatMonth(entry.key), entry.value);
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatisticsReport() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        final stats = paymentProvider.calculateStatistics();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي الطلاب',
                      stats.studentsCount.toString(),
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'طلاب مدفوعين',
                      stats.paidStudentsCount.toString(),
                      Colors.green,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'طلاب متأخرين',
                      stats.overdueStudentsCount.toString(),
                      Colors.red,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'سداد جزئي',
                      stats.partialStudentsCount.toString(),
                      Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              _buildProgressCard(
                'معدل التحصيل',
                stats.collectionRate,
                '%',
                Colors.purple,
              ),
              const SizedBox(height: 16),

              _buildProgressCard(
                'نسبة الطلاب المدفوعين',
                stats.studentsCount > 0
                    ? (stats.paidStudentsCount / stats.studentsCount) * 100
                    : 0,
                '%',
                Colors.green,
              ),
              const SizedBox(height: 16),

              _buildProgressCard(
                'نسبة الطلاب المتأخرين',
                stats.studentsCount > 0
                    ? (stats.overdueStudentsCount / stats.studentsCount) * 100
                    : 0,
                '%',
                Colors.red,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailedReport() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        final filteredPayments = paymentProvider.payments.where((payment) {
          final isInDateRange =
              payment.createdAt.isAfter(_startDate) &&
              payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)));

          if (_selectedGroup == 'الكل') return isInDateRange;

          final groupName = _getGroupName(payment.groupId);
          return isInDateRange && groupName == _selectedGroup;
        }).toList();

        if (filteredPayments.isEmpty) {
          return _buildEmptyState(
            'لا توجد دفعات',
            'لا توجد دفعات في الفترة المحددة',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: filteredPayments.length,
          itemBuilder: (context, index) {
            final payment = filteredPayments[index];
            return _buildPaymentDetailCard(payment);
          },
        );
      },
    );
  }

  Widget _buildOverdueStudentCard(StudentPaymentInfo studentInfo) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.red.withValues(alpha: 0.2),
                child: Icon(Icons.warning, color: Colors.red, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      studentInfo.studentName,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      'المجموعة: ${studentInfo.groupName}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${studentInfo.totalOverdue.toStringAsFixed(0)} ج.م',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          if (studentInfo.overdueReasons.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'أسباب التأخير:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 4),
            ...studentInfo.overdueReasons.map(
              (reason) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 2),
                child: Text(
                  '• $reason',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.red.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRevenueCard(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1.5),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: color.withValues(alpha: 0.2),
            child: Icon(icon, color: color),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
                Text(
                  '${amount.toStringAsFixed(0)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(
    String title,
    double value,
    String unit,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
              Text(
                '${value.toStringAsFixed(1)}$unit',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: value / 100,
            backgroundColor: SimpleTheme.getBorderColor(context),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: SimpleTheme.getTextColor(context),
        ),
      ),
    );
  }

  Widget _buildRevenueItem(String title, double amount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          Text(
            '${amount.toStringAsFixed(0)} ج.م',
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetailCard(Payment payment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getStatusColor(payment.status).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: _getStatusColor(
                  payment.status,
                ).withValues(alpha: 0.2),
                child: Icon(
                  _getStatusIcon(payment.status),
                  color: _getStatusColor(payment.status),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStudentName(payment.studentId),
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      'المجموعة: ${_getGroupName(payment.groupId)}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${payment.totalAmount.toStringAsFixed(0)} ج.م',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getStatusColor(payment.status),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'النوع: ${_getPaymentTypeText(payment.type)}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              Text(
                _formatDate(payment.createdAt),
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: 64,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.4),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(
    DateTime currentDate,
    Function(DateTime) onChanged,
  ) async {
    final date = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: SimpleTheme.primary,
              surface: SimpleTheme.getCardColor(context),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      onChanged(date);
    }
  }

  void _exportReport() {
    // TODO: تنفيذ تصدير التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'ميزة التصدير ستكون متاحة قريباً',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  String _getGroupName(String groupId) {
    final appProvider = context.read<AppProvider>();
    final group = appProvider.groups.firstWhere(
      (g) => g.id == groupId,
      orElse: () => appProvider.groups.first,
    );
    return group.name;
  }

  String _getStudentName(String studentId) {
    final appProvider = context.read<AppProvider>();
    final student = appProvider.students.firstWhere(
      (s) => s.id == studentId,
      orElse: () => appProvider.students.first,
    );
    return student.name;
  }

  String _formatMonth(String monthKey) {
    final parts = monthKey.split('-');
    final year = parts[0];
    final month = int.parse(parts[1]);
    final monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];
    return '${monthNames[month - 1]} $year';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.partial:
        return Colors.blue;
      case PaymentStatus.cancelled:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Icons.check_circle;
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.overdue:
        return Icons.warning;
      case PaymentStatus.partial:
        return Icons.payments;
      case PaymentStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getPaymentTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.monthly:
        return 'شهرية';
      case PaymentType.perSession:
        return 'لكل حصة';
      case PaymentType.perGroup:
        return 'لكل مجموعة';
      case PaymentType.custom:
        return 'مخصصة';
    }
  }
}
