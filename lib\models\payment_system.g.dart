// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_system.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentAdapter extends TypeAdapter<Payment> {
  @override
  final int typeId = 10;

  @override
  Payment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Payment(
      id: fields[0] as String,
      studentId: fields[1] as String,
      groupId: fields[2] as String,
      totalAmount: fields[3] as double,
      paidAmount: fields[4] as double,
      type: fields[5] as PaymentType,
      method: fields[6] as PaymentMethod,
      status: fields[7] as PaymentStatus,
      dueDate: fields[8] as DateTime,
      paidDate: fields[9] as DateTime?,
      notes: fields[10] as String?,
      createdAt: fields[11] as DateTime,
      updatedAt: fields[12] as DateTime?,
      transactions: (fields[13] as List?)?.cast<PaymentTransaction>(),
    );
  }

  @override
  void write(BinaryWriter writer, Payment obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.groupId)
      ..writeByte(3)
      ..write(obj.totalAmount)
      ..writeByte(4)
      ..write(obj.paidAmount)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.method)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.dueDate)
      ..writeByte(9)
      ..write(obj.paidDate)
      ..writeByte(10)
      ..write(obj.notes)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.transactions);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentTransactionAdapter extends TypeAdapter<PaymentTransaction> {
  @override
  final int typeId = 11;

  @override
  PaymentTransaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentTransaction(
      id: fields[0] as String,
      amount: fields[1] as double,
      method: fields[2] as PaymentMethod,
      date: fields[3] as DateTime,
      notes: fields[4] as String?,
      receiptNumber: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentTransaction obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.amount)
      ..writeByte(2)
      ..write(obj.method)
      ..writeByte(3)
      ..write(obj.date)
      ..writeByte(4)
      ..write(obj.notes)
      ..writeByte(5)
      ..write(obj.receiptNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentSettingsAdapter extends TypeAdapter<PaymentSettings> {
  @override
  final int typeId = 15;

  @override
  PaymentSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentSettings(
      policies: (fields[0] as List?)?.cast<PaymentPolicy>(),
      policyLogic: fields[1] as PolicyCombination,
      defaultAmount: fields[2] as double,
      defaultType: fields[3] as PaymentType,
      defaultMethod: fields[4] as PaymentMethod,
      gracePeriodDays: fields[5] as int,
      enableNotifications: fields[6] as bool,
      notificationDaysBefore: fields[7] as int,
      allowPartialPayments: fields[8] as bool,
      createdAt: fields[9] as DateTime,
      updatedAt: fields[10] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentSettings obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.policies)
      ..writeByte(1)
      ..write(obj.policyLogic)
      ..writeByte(2)
      ..write(obj.defaultAmount)
      ..writeByte(3)
      ..write(obj.defaultType)
      ..writeByte(4)
      ..write(obj.defaultMethod)
      ..writeByte(5)
      ..write(obj.gracePeriodDays)
      ..writeByte(6)
      ..write(obj.enableNotifications)
      ..writeByte(7)
      ..write(obj.notificationDaysBefore)
      ..writeByte(8)
      ..write(obj.allowPartialPayments)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentPolicyAdapter extends TypeAdapter<PaymentPolicy> {
  @override
  final int typeId = 16;

  @override
  PaymentPolicy read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentPolicy(
      id: fields[0] as String,
      name: fields[1] as String,
      type: fields[2] as PolicyType,
      isEnabled: fields[3] as bool,
      parameters: (fields[4] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentPolicy obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.isEnabled)
      ..writeByte(4)
      ..write(obj.parameters);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentPolicyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 12;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.pending;
      case 1:
        return PaymentStatus.paid;
      case 2:
        return PaymentStatus.partial;
      case 3:
        return PaymentStatus.overdue;
      case 4:
        return PaymentStatus.cancelled;
      default:
        return PaymentStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.pending:
        writer.writeByte(0);
        break;
      case PaymentStatus.paid:
        writer.writeByte(1);
        break;
      case PaymentStatus.partial:
        writer.writeByte(2);
        break;
      case PaymentStatus.overdue:
        writer.writeByte(3);
        break;
      case PaymentStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentTypeAdapter extends TypeAdapter<PaymentType> {
  @override
  final int typeId = 13;

  @override
  PaymentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentType.monthly;
      case 1:
        return PaymentType.perSession;
      case 2:
        return PaymentType.perGroup;
      case 3:
        return PaymentType.custom;
      default:
        return PaymentType.monthly;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentType obj) {
    switch (obj) {
      case PaymentType.monthly:
        writer.writeByte(0);
        break;
      case PaymentType.perSession:
        writer.writeByte(1);
        break;
      case PaymentType.perGroup:
        writer.writeByte(2);
        break;
      case PaymentType.custom:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 14;

  @override
  PaymentMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentMethod.cash;
      case 1:
        return PaymentMethod.bankTransfer;
      case 2:
        return PaymentMethod.mobileWallet;
      case 3:
        return PaymentMethod.check;
      case 4:
        return PaymentMethod.other;
      default:
        return PaymentMethod.cash;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    switch (obj) {
      case PaymentMethod.cash:
        writer.writeByte(0);
        break;
      case PaymentMethod.bankTransfer:
        writer.writeByte(1);
        break;
      case PaymentMethod.mobileWallet:
        writer.writeByte(2);
        break;
      case PaymentMethod.check:
        writer.writeByte(3);
        break;
      case PaymentMethod.other:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PolicyTypeAdapter extends TypeAdapter<PolicyType> {
  @override
  final int typeId = 17;

  @override
  PolicyType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PolicyType.sessionsCompleted;
      case 1:
        return PolicyType.monthlyDue;
      case 2:
        return PolicyType.lessonCompleted;
      case 3:
        return PolicyType.custom;
      default:
        return PolicyType.sessionsCompleted;
    }
  }

  @override
  void write(BinaryWriter writer, PolicyType obj) {
    switch (obj) {
      case PolicyType.sessionsCompleted:
        writer.writeByte(0);
        break;
      case PolicyType.monthlyDue:
        writer.writeByte(1);
        break;
      case PolicyType.lessonCompleted:
        writer.writeByte(2);
        break;
      case PolicyType.custom:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PolicyTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PolicyCombinationAdapter extends TypeAdapter<PolicyCombination> {
  @override
  final int typeId = 18;

  @override
  PolicyCombination read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PolicyCombination.or;
      case 1:
        return PolicyCombination.and;
      default:
        return PolicyCombination.or;
    }
  }

  @override
  void write(BinaryWriter writer, PolicyCombination obj) {
    switch (obj) {
      case PolicyCombination.or:
        writer.writeByte(0);
        break;
      case PolicyCombination.and:
        writer.writeByte(1);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PolicyCombinationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
