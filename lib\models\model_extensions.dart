import 'student.dart';
import 'group.dart';
import 'lesson.dart';

/// امتدادات للنماذج لدعم التحويل من وإلى JSON
extension StudentJson on Student {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'groupId': groupId,
      'monthlyPayment': monthlyPayment,
      'isPresent': isPresent,
    };
  }

  static Student fromJson(Map<String, dynamic> json) {
    return Student(
      id: json['id'],
      name: json['name'],
      groupId: json['groupId'],
      monthlyPayment: json['monthlyPayment'],
      isPresent: json['isPresent'],
    );
  }
}

extension GroupJson on Group {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'subject': subject,
      'monthlyFee': monthlyFee,
      'schedule': schedule,
    };
  }

  static Group fromJson(Map<String, dynamic> json) {
    return Group(
      id: json['id'],
      name: json['name'],
      subject: json['subject'],
      monthlyFee: json['monthlyFee'],
      schedule: Map<String, List<String>>.from(
        json['schedule'].map(
          (key, value) => MapEntry(key, List<String>.from(value)),
        ),
      ),
    );
  }
}

extension LessonJson on Lesson {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'groupId': groupId,
      'dateTime': dateTime.toIso8601String(),
      'isCompleted': isCompleted,
      'attendedStudentIds': attendedStudentIds,
      'notes': notes,
    };
  }

  static Lesson fromJson(Map<String, dynamic> json) {
    return Lesson(
      id: json['id'],
      groupId: json['groupId'],
      dateTime: DateTime.parse(json['dateTime']),
      isCompleted: json['isCompleted'],
      attendedStudentIds: List<String>.from(json['attendedStudentIds'] ?? []),
      notes: json['notes'] ?? '',
    );
  }
}