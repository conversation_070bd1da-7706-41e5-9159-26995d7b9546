import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';
import 'package:google_fonts/google_fonts.dart';

class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color? color;
  final Color? textColor;
  final IconData? icon;
  final double borderRadius;
  final EdgeInsetsGeometry? padding;
  final bool isOutlined;
  final bool isFullWidth;
  final bool isLoading;
  final Gradient? gradient;

  const ModernButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.color,
    this.textColor,
    this.icon,
    this.borderRadius = 12,
    this.padding,
    this.isOutlined = false,
    this.isFullWidth = false,
    this.isLoading = false,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final buttonColor = color ?? const Color(0xFF6366f1);
    final buttonTextColor = textColor ?? SimpleTheme.getTextColor(context);

    final buttonStyle = isOutlined
        ? ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: buttonColor,
            elevation: 0,
            padding:
                padding ??
                const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
              side: BorderSide(color: buttonColor),
            ),
          )
        : ElevatedButton.styleFrom(
            backgroundColor: gradient != null
                ? Colors.transparent
                : buttonColor,
            foregroundColor: buttonTextColor,
            elevation: 0,
            padding:
                padding ??
                const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            ),
          );

    Widget buttonContent = Row(
      mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading)
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(buttonTextColor),
              ),
            ),
          )
        else if (icon != null)
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(icon, size: 18),
          ),
        Text(
          text,
          style: GoogleFonts.cairo(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ],
    );

    if (gradient != null && !isOutlined) {
      return Ink(
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding:
                padding ??
                const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            width: isFullWidth ? double.infinity : null,
            child: buttonContent,
          ),
        ),
      );
    }

    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonContent,
      ),
    );
  }

  // Factory constructor for creating a gradient button
  factory ModernButton.gradient({
    required String text,
    required VoidCallback onPressed,
    IconData? icon,
    double borderRadius = 12,
    EdgeInsetsGeometry? padding,
    bool isFullWidth = false,
    bool isLoading = false,
    Gradient? gradient,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      icon: icon,
      borderRadius: borderRadius,
      padding: padding,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
      gradient:
          gradient ??
          const LinearGradient(
            colors: [Color(0xFF6366f1), Color(0xFF8B5CF6)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
    );
  }

  // Factory constructor for creating an outlined button
  factory ModernButton.outlined({
    required String text,
    required VoidCallback onPressed,
    Color? color,
    IconData? icon,
    double borderRadius = 12,
    EdgeInsetsGeometry? padding,
    bool isFullWidth = false,
    bool isLoading = false,
  }) {
    return ModernButton(
      text: text,
      onPressed: onPressed,
      color: color ?? const Color(0xFF6366f1),
      icon: icon,
      borderRadius: borderRadius,
      padding: padding,
      isOutlined: true,
      isFullWidth: isFullWidth,
      isLoading: isLoading,
    );
  }
}
