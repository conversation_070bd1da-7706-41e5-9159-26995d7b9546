import 'dart:io';
import 'package:flutter/services.dart';

/// A utility class for installing APK files on Android devices.
/// This is a replacement for the install_plugin_v2 package which had Kotlin version compatibility issues.
class ApkInstaller {
  static const MethodChannel _channel = MethodChannel('com.edutrack.app/apk_installer');

  /// Installs an APK file from the given file path.
  /// 
  /// Returns true if the installation process was started successfully.
  /// Note that this doesn't guarantee the APK was actually installed, just that the intent was launched.
  static Future<bool> installApk(String filePath) async {
    if (!Platform.isAndroid) {
      throw PlatformException(
        code: 'UNSUPPORTED_PLATFORM',
        message: 'APK installation is only supported on Android',
      );
    }

    try {
      final bool? result = await _channel.invokeMethod<bool>('installApk', {'filePath': filePath});
      return result ?? false;
    } on PlatformException catch (e) {
      throw PlatformException(
        code: e.code,
        message: 'Failed to install APK: ${e.message}',
        details: e.details,
      );
    }
  }
}