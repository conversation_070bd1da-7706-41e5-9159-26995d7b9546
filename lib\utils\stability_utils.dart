import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class StabilityUtils {
  static bool _isInitialized = false;
  
  /// Initialize stability optimizations
  static void init() {
    if (_isInitialized) return;
    _isInitialized = true;
    
    try {
      // Handle uncaught errors
      FlutterError.onError = (FlutterErrorDetails details) {
        if (kDebugMode) {
          FlutterError.presentError(details);
        } else {
          // Log error in production but don't crash
          debugPrint('Flutter Error: ${details.exception}');
        }
      };
      
      // Handle platform errors
      PlatformDispatcher.instance.onError = (error, stack) {
        if (kDebugMode) {
          debugPrint('Platform Error: $error');
        }
        return true; // Handled
      };
      
      // Optimize memory usage
      _optimizeMemory();
      
    } catch (e) {
      debugPrint('StabilityUtils init error: $e');
    }
  }
  
  static void _optimizeMemory() {
    try {
      // Reduce image cache size for stability
      PaintingBinding.instance.imageCache.maximumSize = 50;
      PaintingBinding.instance.imageCache.maximumSizeBytes = 20 * 1024 * 1024; // 20MB
      
      // Clear cache periodically
      Future.delayed(const Duration(minutes: 5), () {
        if (PaintingBinding.instance.imageCache.currentSize > 30) {
          PaintingBinding.instance.imageCache.clear();
        }
      });
    } catch (e) {
      debugPrint('Memory optimization error: $e');
    }
  }
  
  /// Safe widget builder that handles errors
  static Widget safeBuilder(Widget Function() builder, {Widget? fallback}) {
    try {
      return builder();
    } catch (e) {
      debugPrint('Widget build error: $e');
      return fallback ?? const SizedBox.shrink();
    }
  }
  
  /// Safe async operation
  static Future<T?> safeAsync<T>(Future<T> Function() operation) async {
    try {
      return await operation();
    } catch (e) {
      debugPrint('Async operation error: $e');
      return null;
    }
  }
}