import 'package:flutter/material.dart';
import '../utils/device_utils.dart';

/// A widget that efficiently renders a list of items with pagination for low-end devices
class PaginatedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext, T, int) itemBuilder;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final int initialPageSize;
  final int pageSize;
  final Widget? loadingIndicator;

  const PaginatedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.initialPageSize = 10,
    this.pageSize = 5,
    this.loadingIndicator,
  });

  @override
  State<PaginatedListView<T>> createState() => _PaginatedListViewState<T>();
}

class _PaginatedListViewState<T> extends State<PaginatedListView<T>> {
  late int _displayCount;
  late ScrollController _scrollController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Start with a smaller number of items for better initial load performance
    _displayCount = widget.initialPageSize.clamp(0, widget.items.length);
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    // Check if we're near the bottom of the list
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _displayCount < widget.items.length) {
      _loadMoreItems();
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate loading delay for better UX
    await Future.delayed(const Duration(milliseconds: 100));

    setState(() {
      _displayCount = (_displayCount + widget.pageSize).clamp(0, widget.items.length);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // For very small lists, use a simple Column instead of ListView for better performance
    if (widget.items.length <= 5 && widget.shrinkWrap) {
      return SingleChildScrollView(
        physics: widget.physics,
        padding: widget.padding,
        controller: _scrollController,
        child: Column(
          children: [
            for (int i = 0; i < widget.items.length; i++)
              RepaintBoundary(
                child: widget.itemBuilder(context, widget.items[i], i),
              ),
          ],
        ),
      );
    }

    // Determine optimal cache extent based on device capability
    final cacheExtent = DeviceUtils.isLowEndDevice() ? 100.0 : 250.0;

    return ListView.builder(
      padding: widget.padding,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      controller: _scrollController,
      itemCount: _displayCount + (_displayCount < widget.items.length ? 1 : 0),
      cacheExtent: cacheExtent,
      itemBuilder: (context, index) {
        if (index == _displayCount) {
          return widget.loadingIndicator ?? const Center(
            child: Padding(
              padding: EdgeInsets.all(8.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }
        
        // Add RepaintBoundary to prevent unnecessary repaints
        return RepaintBoundary(
          child: widget.itemBuilder(context, widget.items[index], index),
        );
      },
      // Optimize scrolling performance
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
    );
  }
}