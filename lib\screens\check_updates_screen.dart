import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../services/app_update_service.dart';
import '../theme/simple_theme.dart';

class CheckUpdatesScreen extends StatefulWidget {
  const CheckUpdatesScreen({super.key});

  @override
  State<CheckUpdatesScreen> createState() => _CheckUpdatesScreenState();
}

class _CheckUpdatesScreenState extends State<CheckUpdatesScreen> {
  bool _isChecking = false;
  String _currentVersion = '';
  String _latestVersion = '';
  String _updateStatus = '';
  bool _hasUpdate = false;

  @override
  void initState() {
    super.initState();
    _getCurrentVersion();
    // التحقق من التحديثات تلقائيًا عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForUpdates();
    });
  }

  Future<void> _getCurrentVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _currentVersion = packageInfo.version;
      });
    } catch (e) {
      debugPrint('خطأ في جلب معلومات التطبيق: $e');
    }
  }

  String? _apkUrl;

  Future<void> _checkForUpdates() async {
    if (_isChecking) return;

    setState(() {
      _isChecking = true;
      _updateStatus = 'جاري التحقق من التحديثات...';
      _hasUpdate = false;
    });

    try {
      // استخدام خدمة التحديث للتحقق من وجود تحديثات جديدة
      // نمرر showDialog: false لمنع ظهور حوار التحديث تلقائيًا
      final result = await AppUpdateService().checkForUpdate(
        context,
        showDialog: false,
      );

      String latestVersion = result['latestVersion'] ?? '';
      _apkUrl = result['apkUrl'] as String?;

      setState(() {
        _isChecking = false;
        _hasUpdate = result['hasUpdate'] ?? false;
        _updateStatus = result['message'] ?? 'تم التحقق من التحديثات';

        // تحديث الإصدار الأحدث إذا كان متوفرًا
        if (latestVersion.isNotEmpty) {
          _latestVersion = latestVersion;
        }
      });
    } catch (e) {
      setState(() {
        _isChecking = false;
        _updateStatus = 'حدث خطأ أثناء التحقق من التحديثات';
      });
      debugPrint('خطأ في التحقق من التحديثات: $e');
    }
  }

  Future<void> _downloadAndInstallUpdate() async {
    if (_apkUrl == null || _apkUrl!.isEmpty) {
      setState(() {
        _updateStatus = 'رابط التحديث غير صالح';
      });
      return;
    }

    // استخدام خدمة التحديث لتحميل وتثبيت التحديث
    try {
      setState(() {
        _isChecking = true;
        _updateStatus = 'جاري تحميل التحديث...';
      });

      // استدعاء دالة تحميل وتثبيت التحديث من خدمة التحديث
      await AppUpdateService().downloadAndInstallUpdate(_apkUrl!, context);

      // لن يتم تنفيذ هذا الكود إلا إذا فشل التثبيت
      setState(() {
        _isChecking = false;
        _updateStatus = 'تم تحميل التحديث وبدء التثبيت';
      });
    } catch (e) {
      setState(() {
        _isChecking = false;
        _updateStatus = 'حدث خطأ أثناء تحميل التحديث';
      });
      debugPrint('خطأ في تحميل التحديث: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'التحقق من التحديثات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).scaffoldBackgroundColor,
              Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة التحديث
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366f1), Color(0xFFec4899)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: _isChecking
                      ? CircularProgressIndicator(
                          color: SimpleTheme.getTextColor(context),
                          strokeWidth: 3,
                        )
                      : Icon(
                          Icons.system_update,
                          size: 60,
                          color: SimpleTheme.getTextColor(context),
                        ),
                ),
                const SizedBox(height: 40),

                // معلومات الإصدار الحالي
                Text(
                  'الإصدار الحالي',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _currentVersion,
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF6366f1),
                  ),
                ),
                const SizedBox(height: 10),

                // معلومات الإصدار الأحدث
                if (_latestVersion.isNotEmpty && _hasUpdate) ...[
                  Text(
                    'الإصدار الأحدث',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _latestVersion,
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
                const SizedBox(height: 20),

                // حالة التحديث
                Text(
                  _updateStatus,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: _hasUpdate
                        ? Colors.green
                        : (_isChecking
                              ? SimpleTheme.getTextColor(
                                  context,
                                ).withValues(alpha: 0.7)
                              : Colors.orange),
                    fontWeight: _hasUpdate || _isChecking
                        ? FontWeight.normal
                        : FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                // رسالة توضيحية
                if (!_isChecking && !_hasUpdate) ...[
                  const SizedBox(height: 10),
                  Text(
                    'يتم التحقق من التحديثات فقط من خلال هذه الشاشة',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: SimpleTheme.getTextColor(
                        context,
                      ).withValues(alpha: 0.54),
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                const SizedBox(height: 40),

                // زر التحقق من التحديثات أو تحديث التطبيق
                ElevatedButton(
                  onPressed: _isChecking
                      ? null
                      : _hasUpdate
                      ? _downloadAndInstallUpdate
                      : _checkForUpdates,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _hasUpdate
                        ? Colors.green
                        : const Color(0xFF6366f1),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 15,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    minimumSize: const Size(250, 50),
                  ),
                  child: Text(
                    _isChecking
                        ? 'جاري التحقق...'
                        : (_hasUpdate
                              ? 'تحديث التطبيق'
                              : 'التحقق من التحديثات'),
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ),

                // زر إعادة التحقق إذا لم يكن هناك تحديث
                if (!_isChecking &&
                    _updateStatus.isNotEmpty &&
                    _updateStatus != 'جاري التحقق من التحديثات...') ...[
                  const SizedBox(height: 10),
                  TextButton(
                    onPressed: _checkForUpdates,
                    child: Text(
                      'إعادة التحقق',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: SimpleTheme.getTextColor(
                          context,
                        ).withValues(alpha: 0.7),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
