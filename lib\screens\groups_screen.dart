import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../widgets/modern_card.dart';
import '../widgets/modern_button.dart';
import '../models/group.dart';
import '../models/student.dart';
import '../theme/simple_theme.dart';

class GroupsScreen extends StatefulWidget {
  const GroupsScreen({super.key});

  @override
  State<GroupsScreen> createState() => _GroupsScreenState();
}

class _GroupsScreenState extends State<GroupsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: SimpleTheme.getBackgroundGradient(context),
            ),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Section
                    _buildHeader(context, provider),
                    const SizedBox(height: 24),

                    // Stats Cards
                    _buildStatsCards(provider),
                    const SizedBox(height: 24),

                    // Search Section
                    _buildSearch(provider),
                    const SizedBox(height: 20),

                    // Groups List
                    _buildGroupsList(provider),

                    // إضافة مساحة إضافية في الأسفل للـ FAB
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => _showAddGroupDialog(context, provider),
            backgroundColor: SimpleTheme.primary,
            foregroundColor: SimpleTheme.getTextColor(context),
            elevation: 4,
            child: const Icon(Icons.add_rounded),
          ),
        );
      },
    );
  }

  // بناء الهيدر
  Widget _buildHeader(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // العنوان والوصف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المجموعات',
                  style: GoogleFonts.cairo(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                    shadows: [
                      Shadow(
                        color: SimpleTheme.getContainerColor(context),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة وتنظيم مجموعات الطلاب',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء بطاقات الإحصائيات البسيطة
  Widget _buildStatsCards(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: SimpleTheme.getBorderColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSimpleStatCard(
              'المجموعات',
              provider.groups.length.toString(),
              Icons.groups_rounded,
              const Color(0xFF6366f1),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: SimpleTheme.getBorderColor(context),
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          Expanded(
            child: _buildSimpleStatCard(
              'الطلاب',
              provider.totalStudents.toString(),
              Icons.person_rounded,
              const Color(0xFF10b981),
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: SimpleTheme.getBorderColor(context),
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),
          Expanded(
            child: _buildSimpleStatCard(
              'المواد',
              provider.groups.map((g) => g.subject).toSet().length.toString(),
              Icons.book_rounded,
              const Color(0xFFf59e0b),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 8),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: SimpleTheme.getSecondaryTextColor(context),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // بناء البحث
  Widget _buildSearch(AppProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: SimpleTheme.getBorderColor(context)),
      ),
      child: TextField(
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          hintText: 'البحث في المجموعات...',
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSubtitleColor(context),
          ),
          border: InputBorder.none,
          icon: Icon(Icons.search, color: SimpleTheme.getIconColor(context)),
        ),
        onChanged: (value) => setState(() => _searchQuery = value),
      ),
    );
  }

  // بناء قائمة المجموعات
  Widget _buildGroupsList(AppProvider provider) {
    var filteredGroups = provider.groups.where((group) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          group.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          group.subject.toLowerCase().contains(_searchQuery.toLowerCase());

      return matchesSearch;
    }).toList();

    if (filteredGroups.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: filteredGroups.map((group) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _GroupCard(group: group),
        );
      }).toList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: SimpleTheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.group_add, size: 64, color: SimpleTheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مجموعات بعد',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الزر أدناه لإضافة مجموعة جديدة',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddGroupDialog(
              context,
              Provider.of<AppProvider>(context, listen: false),
            ),
            icon: const Icon(Icons.add_rounded, size: 20),
            label: Text(
              'إضافة مجموعة جديدة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: SimpleTheme.primary,
              foregroundColor: SimpleTheme.getTextColor(context),
              elevation: 2,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddGroupDialog(BuildContext context, AppProvider provider) {
    final nameController = TextEditingController();
    final subjectController = TextEditingController();
    final feeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            gradient: SimpleTheme.getCardGradient(context),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: SimpleTheme.getBorderColor(context),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: SimpleTheme.getContainerColor(context),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'إضافة مجموعة جديدة',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        color: SimpleTheme.getIconColor(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                _buildDialogTextField(
                  controller: nameController,
                  label: 'اسم المجموعة',
                  icon: Icons.group,
                  hint: 'مثال: مجموعة الرياضيات المتقدمة',
                ),
                const SizedBox(height: 16),
                _buildDialogTextField(
                  controller: subjectController,
                  label: 'المادة الدراسية',
                  icon: Icons.book,
                  hint: 'مثال: الرياضيات',
                ),
                const SizedBox(height: 16),
                _buildDialogTextField(
                  controller: feeController,
                  label: 'الرسوم الشهرية',
                  icon: Icons.attach_money,
                  hint: 'مثال: 500',
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: _buildDialogButton(
                        text: 'إلغاء',
                        onPressed: () => Navigator.pop(context),
                        isSecondary: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDialogButton(
                        text: 'إضافة',
                        onPressed: () => _addGroup(
                          context,
                          provider,
                          nameController.text,
                          subjectController.text,
                          feeController.text,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    TextInputType? keyboardType,
  }) {
    return ModernCard(
      padding: EdgeInsets.zero,
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
          hintStyle: GoogleFonts.cairo(
            color: SimpleTheme.getSubtitleColor(context),
          ),
          prefixIcon: Icon(icon, color: const Color(0xFF6366f1)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildDialogButton({
    required String text,
    required VoidCallback onPressed,
    bool isSecondary = false,
  }) {
    return isSecondary
        ? ModernButton.outlined(
            text: text,
            onPressed: onPressed,
            isFullWidth: true,
          )
        : ModernButton.gradient(
            text: text,
            onPressed: onPressed,
            isFullWidth: true,
          );
  }

  void _addGroup(
    BuildContext context,
    AppProvider provider,
    String name,
    String subject,
    String feeText,
  ) {
    if (name.isEmpty || subject.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى ملء جميع الحقول المطلوبة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final fee = double.tryParse(feeText) ?? 0.0;

    final group = Group(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      subject: subject,
      monthlyFee: fee,
    );

    provider.addGroup(group);

    Navigator.pop(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة المجموعة بنجاح', style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF6366f1),
      ),
    );
  }
}

class _GroupCard extends StatefulWidget {
  final Group group;

  const _GroupCard({required this.group});

  @override
  State<_GroupCard> createState() => _GroupCardState();
}

class _GroupCardState extends State<_GroupCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final students = provider.getStudentsByGroup(widget.group.id);

        return ModernCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () => setState(() => isExpanded = !isExpanded),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  widget.group.name,
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: SimpleTheme.getTextColor(context),
                                  ),
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      backgroundColor: const Color(0xFF1e293b),
                                      title: Text(
                                        'حذف المجموعة',
                                        style: GoogleFonts.cairo(
                                          color: SimpleTheme.getTextColor(
                                            context,
                                          ),
                                        ),
                                      ),
                                      content: Text(
                                        'هل أنت متأكد من حذف هذه المجموعة؟ سيتم حذف جميع الطلاب المرتبطين بها.',
                                        style: GoogleFonts.cairo(
                                          color: SimpleTheme.getTextColor(
                                            context,
                                          ).withValues(alpha: 0.7),
                                        ),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          child: Text(
                                            'إلغاء',
                                            style: GoogleFonts.cairo(
                                              color: SimpleTheme.getTextColor(
                                                context,
                                              ).withValues(alpha: 0.7),
                                            ),
                                          ),
                                        ),
                                        TextButton(
                                          onPressed: () {
                                            provider.deleteGroup(
                                              widget.group.id,
                                            );
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            'حذف',
                                            style: GoogleFonts.cairo(
                                              color: Colors.red,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                                icon: const Icon(
                                  Icons.delete,
                                  color: Colors.red,
                                  size: 20,
                                ),
                              ),
                              IconButton(
                                onPressed: () {
                                  _showEditGroupDialog(context, provider);
                                },
                                icon: const Icon(
                                  Icons.edit,
                                  color: Color(0xFF6366f1),
                                  size: 20,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.group.subject,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: SimpleTheme.getTextColor(
                                context,
                              ).withValues(alpha: 0.7),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              _buildInfoChip(
                                '${students.length} طالب',
                                Icons.person,
                              ),
                              const SizedBox(width: 8),
                              _buildInfoChip(
                                '${widget.group.monthlyFee} ر.س',
                                Icons.attach_money,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ],
                ),
              ),
              if (isExpanded) ...[
                const SizedBox(height: 16),
                Divider(
                  color: SimpleTheme.getTextColor(
                    context,
                  ).withValues(alpha: 0.3),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الطلاب',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _showAddStudentDialog(context, provider),
                      icon: const Icon(Icons.add, color: Color(0xFF6366f1)),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ...students.map(
                  (student) => _buildStudentTile(student, provider),
                ),
                if (students.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: Text(
                        'لا يوجد طلاب في هذه المجموعة',
                        style: GoogleFonts.cairo(
                          color: SimpleTheme.getTextColor(
                            context,
                          ).withValues(alpha: 0.6),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF6366f1).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: const Color(0xFF6366f1)),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentTile(Student student, AppProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: ModernCard(
        padding: const EdgeInsets.all(12),
        color: SimpleTheme.getContainerColor(context),
        child: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFF6366f1).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  student.name.isNotEmpty ? student.name[0] : '?',
                  style: GoogleFonts.cairo(
                    color: const Color(0xFF6366f1),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    student.name,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                      fontSize: 14,
                    ),
                  ),
                  if (student.phoneNumber != null &&
                      student.phoneNumber!.isNotEmpty)
                    Text(
                      student.phoneNumber!,
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
            Text(
              '${student.monthlyPayment} ر.س',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context).withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () {
                _showEditStudentDialog(context, provider, student);
              },
              icon: const Icon(Icons.edit, color: Color(0xFF6366f1), size: 20),
            ),
            IconButton(
              onPressed: () {
                provider.deleteStudent(student.id);
              },
              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddStudentDialog(BuildContext context, AppProvider provider) {
    final nameController = TextEditingController();
    final paymentController = TextEditingController();
    final phoneController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                SimpleTheme.getCardColor(context),
                SimpleTheme.getContainerColor(context),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: SimpleTheme.getBorderColor(context),
              width: 1,
            ),
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'إضافة طالب جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.close,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: nameController,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'اسم الطالب',
                      hintText: 'مثال: محمد أحمد',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      hintStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSubtitleColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.person,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: paymentController
                      ..text = widget.group.monthlyFee.toString(),
                    enabled: false,
                    keyboardType: TextInputType.number,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'الرسوم الشهرية (من المجموعة)',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.attach_money,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: phoneController,
                    keyboardType: TextInputType.phone,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'رقم الهاتف (اختياري)',
                      hintText: 'مثال: 0501234567 أو رقم ولي الأمر',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      hintStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSubtitleColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.phone,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: ModernButton.outlined(
                        text: 'إلغاء',
                        onPressed: () => Navigator.pop(context),
                        isFullWidth: true,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ModernButton.gradient(
                        text: 'إضافة',
                        onPressed: () {
                          if (nameController.text.isNotEmpty) {
                            final student = Student(
                              id: DateTime.now().millisecondsSinceEpoch
                                  .toString(),
                              name: nameController.text,
                              groupId: widget.group.id,
                              monthlyPayment: widget.group.monthlyFee,
                              phoneNumber: phoneController.text.isNotEmpty
                                  ? phoneController.text
                                  : null,
                            );
                            provider.addStudent(student);
                            Navigator.pop(context);

                            // إظهار رسالة نجاح
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تم إضافة الطالب بنجاح',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: SimpleTheme.primary,
                              ),
                            );
                          } else {
                            // إظهار رسالة خطأ
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'يرجى إدخال اسم الطالب',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        isFullWidth: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // نموذج تعديل المجموعة
  void _showEditGroupDialog(BuildContext context, AppProvider provider) {
    final nameController = TextEditingController(text: widget.group.name);
    final subjectController = TextEditingController(text: widget.group.subject);
    final feeController = TextEditingController(
      text: widget.group.monthlyFee.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                SimpleTheme.getCardColor(context),
                SimpleTheme.getContainerColor(context),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: SimpleTheme.getContainerColor(context),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: SimpleTheme.modernGradient,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.edit,
                        color: SimpleTheme.getIconColor(context),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'تعديل المجموعة',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: nameController,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'اسم المجموعة',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.group,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: subjectController,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'المادة',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.book,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ModernCard(
                  padding: EdgeInsets.zero,
                  child: TextField(
                    controller: feeController,
                    keyboardType: TextInputType.number,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                    decoration: InputDecoration(
                      labelText: 'الرسوم الشهرية',
                      labelStyle: GoogleFonts.cairo(
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                      prefixIcon: const Icon(
                        Icons.attach_money,
                        color: Color(0xFF6366f1),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),

                const SizedBox(height: 32),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey.shade600,
                          foregroundColor: SimpleTheme.getTextColor(context),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ModernButton.gradient(
                        text: 'حفظ التغييرات',
                        onPressed: () {
                          if (nameController.text.isNotEmpty &&
                              subjectController.text.isNotEmpty) {
                            final updatedGroup = Group(
                              id: widget.group.id,
                              name: nameController.text,
                              subject: subjectController.text,
                              monthlyFee:
                                  double.tryParse(feeController.text) ?? 0.0,
                              createdAt: widget.group.createdAt,
                            );
                            provider.updateGroup(updatedGroup);
                            Navigator.pop(context);

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'تم تحديث المجموعة بنجاح',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: SimpleTheme.primary,
                              ),
                            );
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'يرجى ملء جميع الحقول المطلوبة',
                                  style: GoogleFonts.cairo(),
                                ),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // نموذج تعديل الطالب
  void _showEditStudentDialog(
    BuildContext context,
    AppProvider provider,
    Student student,
  ) {
    final nameController = TextEditingController(text: student.name);
    final phoneController = TextEditingController(
      text: student.phoneNumber ?? '',
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                SimpleTheme.getCardColor(context),
                SimpleTheme.getContainerColor(context),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: SimpleTheme.getBorderColor(context),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: SimpleTheme.modernGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.edit,
                      color: SimpleTheme.getIconColor(context),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'تعديل الطالب',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              ModernCard(
                padding: EdgeInsets.zero,
                child: TextField(
                  controller: nameController,
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  decoration: InputDecoration(
                    labelText: 'اسم الطالب',
                    labelStyle: GoogleFonts.cairo(
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                    prefixIcon: const Icon(
                      Icons.person,
                      color: Color(0xFF6366f1),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              ModernCard(
                padding: EdgeInsets.zero,
                child: TextField(
                  controller: phoneController,
                  keyboardType: TextInputType.phone,
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  decoration: InputDecoration(
                    labelText: 'رقم الهاتف (اختياري)',
                    labelStyle: GoogleFonts.cairo(
                      color: SimpleTheme.getSecondaryTextColor(context),
                    ),
                    prefixIcon: const Icon(
                      Icons.phone,
                      color: Color(0xFF6366f1),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade600,
                        foregroundColor: SimpleTheme.getTextColor(context),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'إلغاء',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (nameController.text.isNotEmpty) {
                          final updatedStudent = Student(
                            id: student.id,
                            name: nameController.text,
                            groupId: student.groupId,
                            monthlyPayment: student.monthlyPayment,
                            isPresent: student.isPresent,
                            hasPaid: student.hasPaid,
                            lastAttendance: student.lastAttendance,
                            phoneNumber: phoneController.text.isNotEmpty
                                ? phoneController.text
                                : null,
                            notes: student.notes,
                          );
                          provider.updateStudent(updatedStudent);
                          Navigator.pop(context);

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'تم تحديث الطالب بنجاح',
                                style: GoogleFonts.cairo(),
                              ),
                              backgroundColor: SimpleTheme.primary,
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'يرجى إدخال اسم الطالب',
                                style: GoogleFonts.cairo(),
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: SimpleTheme.primary,
                        foregroundColor: SimpleTheme.getTextColor(context),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'حفظ التغييرات',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
