import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/notification_service.dart';
import '../theme/simple_theme.dart';
import '../widgets/simple_background.dart';
import '../models/lesson.dart';

/// صفحة اختبار الإشعارات
class NotificationTestScreen extends StatefulWidget {
  const NotificationTestScreen({super.key});

  @override
  State<NotificationTestScreen> createState() => _NotificationTestScreenState();
}

class _NotificationTestScreenState extends State<NotificationTestScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'اختبار الإشعارات',
          style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: SimpleTheme.cardBg,
        elevation: 0,
      ),
      body: SimpleBackground(
        child: <PERSON>View(
          padding: const EdgeInsets.all(16),
          children: [
            // إشعار فوري
            _buildTestCard(
              'إشعار فوري',
              'اختبار الإشعارات الفورية',
              Icons.notifications,
              () => _testInstantNotification(),
            ),

            // تذكير حصة
            _buildTestCard(
              'تذكير حصة',
              'اختبار تذكير الحصة بعد 5 ثوان',
              Icons.school,
              () => _testLessonReminder(),
            ),

            // تذكير امتحان
            _buildTestCard(
              'تذكير امتحان',
              'اختبار تذكير الامتحان بعد 10 ثوان',
              Icons.quiz,
              () => _testExamReminder(),
            ),

            // مهمة يومية
            _buildTestCard(
              'مهمة يومية',
              'اختبار المهام اليومية',
              Icons.task_alt,
              () => _testDailyTask(),
            ),

            // منبه ذكي
            _buildTestCard(
              'منبه ذكي',
              'اختبار المنبه الذكي بعد 15 ثانية',
              Icons.alarm,
              () => _testSmartAlarm(),
            ),

            const SizedBox(height: 24),

            // إلغاء جميع الإشعارات
            _buildTestCard(
              'إلغاء جميع الإشعارات',
              'إلغاء جميع الإشعارات المجدولة',
              Icons.cancel,
              () => _cancelAllNotifications(),
              isDestructive: true,
            ),

            const SizedBox(height: 24),

            // معلومات الإشعارات المجدولة
            _buildInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: SimpleTheme.cardBg,
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (isDestructive ? Colors.red : SimpleTheme.primary)
                .withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isDestructive ? Colors.red : SimpleTheme.primary,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            color: SimpleTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(color: SimpleTheme.textMuted, fontSize: 12),
        ),
        trailing: Icon(Icons.arrow_forward_ios, color: SimpleTheme.getIconColor(context)),
        onTap: onTap,
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: SimpleTheme.cardBg,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات مهمة',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: SimpleTheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '• تأكد من تفعيل الإشعارات في إعدادات الجهاز\n'
              '• قد تحتاج لإعطاء إذن الإشعارات للتطبيق\n'
              '• الإشعارات المجدولة ستظهر حتى لو كان التطبيق مغلق\n'
              '• يمكن إلغاء الإشعارات من خلال الزر أعلاه',
              style: GoogleFonts.cairo(
                color: SimpleTheme.textMuted,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testInstantNotification() async {
    await NotificationService.showInstantNotification(
      'إشعار تجريبي',
      'هذا إشعار فوري للاختبار! 🎉',
      priority: NotificationPriority.high,
    );

    _showSnackBar('تم إرسال الإشعار الفوري');
  }

  Future<void> _testLessonReminder() async {
    final lesson = Lesson(
      id: 'test_lesson',
      groupId: 'test_group',
      dateTime: DateTime.now().add(const Duration(seconds: 5)),
      subject: 'الرياضيات',
    );

    await NotificationService.scheduleLessonReminder(
      lesson,
      const Duration(seconds: 0),
    );

    _showSnackBar('تم جدولة تذكير الحصة (5 ثوان)');
  }

  Future<void> _testExamReminder() async {
    await NotificationService.scheduleExamReminder(
      'امتحان الفيزياء',
      DateTime.now().add(const Duration(seconds: 10)),
      const Duration(seconds: 0),
    );

    _showSnackBar('تم جدولة تذكير الامتحان (10 ثوان)');
  }

  Future<void> _testDailyTask() async {
    final now = DateTime.now();
    await NotificationService.scheduleDailyTask(
      'مراجعة الحضور',
      'حان وقت مراجعة حضور الطلاب اليومي',
      TimeOfDay(hour: now.hour, minute: now.minute + 1),
    );

    _showSnackBar('تم جدولة المهمة اليومية (دقيقة واحدة)');
  }

  Future<void> _testSmartAlarm() async {
    final lessons = [
      Lesson(
        id: 'lesson1',
        groupId: 'group1',
        dateTime: DateTime.now().add(const Duration(seconds: 15)),
        subject: 'الرياضيات',
      ),
    ];

    await NotificationService.scheduleSmartAlarm(
      lessons,
      const Duration(seconds: 0),
    );

    _showSnackBar('تم جدولة المنبه الذكي (15 ثانية)');
  }

  Future<void> _cancelAllNotifications() async {
    await NotificationService.cancelAllNotifications();
    _showSnackBar('تم إلغاء جميع الإشعارات المجدولة');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: SimpleTheme.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
