import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          'سياسة الخصوصية',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.bold,
            fontSize: 22,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: SimpleTheme.modernGradient,
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              SimpleTheme.getBackgroundColor(context),
              SimpleTheme.getCardColor(context),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الصفحة مع أيقونة
                _buildPageHeader(),
                const SizedBox(height: 30),

                _buildSectionHeader('مقدمة'),
                _buildParagraph(
                  'نحن في تطبيق EduTrack نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. '
                  'تشرح سياسة الخصوصية هذه كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.',
                ),

                _buildSectionHeader('البيانات التي نجمعها'),
                _buildParagraph(
                  'يقوم تطبيق EduTrack بتخزين جميع البيانات محلياً على جهازك فقط، ولا يتم إرسال أي بيانات إلى خوادم خارجية. '
                  'البيانات المخزنة تشمل:',
                ),
                _buildBulletPoint('معلومات الطلاب والمجموعات التعليمية'),
                _buildBulletPoint('سجلات الحضور والغياب'),
                _buildBulletPoint('جداول الدروس والمواعيد'),
                _buildBulletPoint('سجلات المدفوعات'),
                _buildBulletPoint('إعدادات التطبيق وتفضيلات المستخدم'),

                _buildSectionHeader('كيفية استخدام البيانات'),
                _buildParagraph(
                  'نستخدم البيانات التي تقوم بإدخالها في التطبيق فقط لتوفير الخدمات التالية:',
                ),
                _buildBulletPoint('إدارة المجموعات والطلاب'),
                _buildBulletPoint('تتبع الحضور والغياب'),
                _buildBulletPoint('إدارة الجداول الدراسية'),
                _buildBulletPoint('متابعة المدفوعات'),
                _buildBulletPoint('إنشاء النسخ الاحتياطية واستعادتها'),

                _buildSectionHeader('حماية البيانات'),
                _buildParagraph('نحن نتخذ الإجراءات التالية لحماية بياناتك:'),
                _buildBulletPoint('تخزين جميع البيانات محلياً على جهازك فقط'),
                _buildBulletPoint('عدم مشاركة أي بيانات مع أطراف ثالثة'),
                _buildBulletPoint('تشفير النسخ الاحتياطية لحماية البيانات'),
                _buildBulletPoint('عدم جمع أي معلومات تعريف شخصية دون موافقتك'),

                _buildSectionHeader('التحديثات والاتصال بالإنترنت'),
                _buildParagraph(
                  'يستخدم التطبيق الاتصال بالإنترنت فقط للتحقق من وجود تحديثات جديدة. '
                  'لا يتم إرسال أي بيانات شخصية أو معلومات عن استخدامك للتطبيق أثناء هذه العملية.',
                ),

                _buildSectionHeader('حقوقك'),
                _buildParagraph('لديك الحق الكامل في:'),
                _buildBulletPoint('الوصول إلى بياناتك المخزنة في التطبيق'),
                _buildBulletPoint('تصحيح أي معلومات غير دقيقة'),
                _buildBulletPoint('حذف بياناتك من التطبيق في أي وقت'),
                _buildBulletPoint('نقل بياناتك عبر ميزة النسخ الاحتياطي'),

                _buildSectionHeader('التغييرات على سياسة الخصوصية'),
                _buildParagraph(
                  'قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. سيتم إعلامك بأي تغييرات مهمة '
                  'من خلال إشعار داخل التطبيق أو عند تثبيت تحديث جديد.',
                ),

                _buildSectionHeader('اتصل بنا'),
                _buildParagraph(
                  'إذا كان لديك أي أسئلة أو استفسارات حول سياسة الخصوصية، يرجى التواصل معنا على:',
                ),
                _buildParagraph('البريد الإلكتروني: <EMAIL>'),

                const SizedBox(height: 40),

                // تاريخ التحديث مع تصميم محسن
                Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    decoration: BoxDecoration(
                      gradient: SimpleTheme.modernGradient,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: SimpleTheme.primary.withValues(alpha: 0.4),
                          blurRadius: 20,
                          spreadRadius: 2,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.update,
                          color: SimpleTheme.getIconColor(context),
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'آخر تحديث: ديسمبر 2024',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: SimpleTheme.getTextColor(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 32, bottom: 16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              SimpleTheme.primary.withValues(alpha: 0.15),
              SimpleTheme.primary.withValues(alpha: 0.05),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: SimpleTheme.primary.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: SimpleTheme.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: SimpleTheme.primary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getSectionIcon(title),
                color: SimpleTheme.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'مقدمة':
        return Icons.info_outline;
      case 'البيانات التي نجمعها':
        return Icons.data_usage_outlined;
      case 'كيفية استخدام البيانات':
        return Icons.settings_applications_outlined;
      case 'حماية البيانات':
        return Icons.security_outlined;
      case 'التحديثات والاتصال بالإنترنت':
        return Icons.cloud_sync_outlined;
      case 'حقوقك':
        return Icons.account_balance_outlined;
      case 'التغييرات على سياسة الخصوصية':
        return Icons.update_outlined;
      case 'اتصل بنا':
        return Icons.contact_support_outlined;
      default:
        return Icons.article_outlined;
    }
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20, left: 8, right: 8),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: SimpleTheme.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: SimpleTheme.primary.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          text,
          style: GoogleFonts.cairo(
            fontSize: 16,
            height: 1.7,
            color: Colors.white.withValues(alpha: 0.9),
          ),
          textAlign: TextAlign.justify,
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(right: 20, bottom: 16, left: 8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: SimpleTheme.getCardColor(context).withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: SimpleTheme.primary.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 2, left: 12),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: SimpleTheme.modernGradient,
                boxShadow: [
                  BoxShadow(
                    color: SimpleTheme.primary.withValues(alpha: 0.3),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Icon(
                Icons.check,
                color: SimpleTheme.getIconColor(context),
                size: 12,
              ),
            ),
            Expanded(
              child: Text(
                text,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  height: 1.6,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SimpleTheme.primary.withValues(alpha: 0.1),
            SimpleTheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: SimpleTheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: SimpleTheme.modernGradient,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: SimpleTheme.primary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              Icons.privacy_tip_outlined,
              color: SimpleTheme.getIconColor(context),
              size: 32,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حماية خصوصيتك',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'نلتزم بحماية بياناتك وخصوصيتك',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
