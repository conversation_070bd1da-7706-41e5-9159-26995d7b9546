import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';

/// نظام التنقل المحسن مع الإيماءات
class EnhancedNavigation extends StatefulWidget {
  final List<Widget> screens;
  final int currentIndex;
  final Function(int) onPageChanged;
  final bool enableSwipeGestures;

  const EnhancedNavigation({
    super.key,
    required this.screens,
    required this.currentIndex,
    required this.onPageChanged,
    this.enableSwipeGestures = true,
  });

  @override
  State<EnhancedNavigation> createState() => _EnhancedNavigationState();
}

class _EnhancedNavigationState extends State<EnhancedNavigation> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: widget.onPageChanged,
      physics: widget.enableSwipeGestures
          ? const BouncingScrollPhysics()
          : const NeverScrollableScrollPhysics(),
      itemCount: widget.screens.length,
      itemBuilder: (context, index) {
        return widget.screens[index];
      },
    );
  }

  /// الانتقال إلى صفحة معينة
  void animateToPage(int page) {
    _pageController.animateToPage(
      page,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}

/// شريط البحث العام
class GlobalSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final String hintText;

  const GlobalSearchBar({
    super.key,
    required this.onSearch,
    this.hintText = 'البحث في جميع البيانات...',
  });

  @override
  State<GlobalSearchBar> createState() => _GlobalSearchBarState();
}

class _GlobalSearchBarState extends State<GlobalSearchBar> {
  final TextEditingController _controller = TextEditingController();
  bool _isExpanded = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: _isExpanded ? MediaQuery.of(context).size.width - 32 : 48,
      height: 48,
      decoration: BoxDecoration(
        color: SimpleTheme.cardBg,
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: SimpleTheme.getTextColor(context).withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              _isExpanded ? Icons.arrow_back : Icons.search,
              color: SimpleTheme.primary,
            ),
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
                if (!_isExpanded) {
                  _controller.clear();
                  widget.onSearch('');
                }
              });
            },
          ),
          if (_isExpanded)
            Expanded(
              child: TextField(
                controller: _controller,
                style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: GoogleFonts.cairo(color: SimpleTheme.textMuted),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                onChanged: widget.onSearch,
                autofocus: true,
              ),
            ),
        ],
      ),
    );
  }
}

/// الأزرار العائمة للاختصارات السريعة
class QuickActionFAB extends StatefulWidget {
  final List<QuickAction> actions;

  const QuickActionFAB({super.key, required this.actions});

  @override
  State<QuickActionFAB> createState() => _QuickActionFABState();
}

class _QuickActionFABState extends State<QuickActionFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...widget.actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;

          return AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.scale(
                scale: _animation.value,
                child: Transform.translate(
                  offset: Offset(0, _animation.value * (index + 1) * -70.0),
                  child: Opacity(
                    opacity: _animation.value,
                    child: FloatingActionButton(
                      mini: true,
                      heroTag: action.label,
                      backgroundColor: action.color,
                      onPressed: action.onPressed,
                      child: Icon(
                        action.icon,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }),

        FloatingActionButton(
          onPressed: _toggleExpansion,
          backgroundColor: SimpleTheme.primary,
          child: AnimatedRotation(
            turns: _isExpanded ? 0.125 : 0,
            duration: const Duration(milliseconds: 300),
            child: Icon(Icons.add, color: SimpleTheme.getIconColor(context)),
          ),
        ),
      ],
    );
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }
}

/// نموذج للإجراء السريع
class QuickAction {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;

  const QuickAction({
    required this.label,
    required this.icon,
    required this.color,
    required this.onPressed,
  });
}

/// شريط الصفحات المفضلة
class FavoritePagesBar extends StatelessWidget {
  final List<FavoritePage> favoritePages;
  final Function(int) onPageSelected;

  const FavoritePagesBar({
    super.key,
    required this.favoritePages,
    required this.onPageSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: SimpleTheme.cardBg,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: SimpleTheme.getTextColor(context).withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemCount: favoritePages.length,
        itemBuilder: (context, index) {
          final page = favoritePages[index];
          return GestureDetector(
            onTap: () => onPageSelected(page.pageIndex),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: page.isActive
                    ? SimpleTheme.primary.withValues(alpha: 0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    page.icon,
                    color: page.isActive
                        ? SimpleTheme.primary
                        : SimpleTheme.textMuted,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    page.title,
                    style: GoogleFonts.cairo(
                      color: page.isActive
                          ? SimpleTheme.primary
                          : SimpleTheme.textMuted,
                      fontSize: 12,
                      fontWeight: page.isActive
                          ? FontWeight.w600
                          : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// نموذج للصفحة المفضلة
class FavoritePage {
  final String title;
  final IconData icon;
  final int pageIndex;
  final bool isActive;

  const FavoritePage({
    required this.title,
    required this.icon,
    required this.pageIndex,
    this.isActive = false,
  });
}

/// مؤشر الصفحة المحسن
class EnhancedPageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Color activeColor;
  final Color inactiveColor;

  const EnhancedPageIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.activeColor = SimpleTheme.primary,
    this.inactiveColor = SimpleTheme.textMuted,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalPages, (index) {
        final isActive = index == currentPage;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: isActive ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: isActive ? activeColor : inactiveColor,
            borderRadius: BorderRadius.circular(4),
          ),
        );
      }),
    );
  }
}
