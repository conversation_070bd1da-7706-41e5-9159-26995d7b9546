import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as path;

import '../models/model_extensions.dart';
import 'data_service.dart';

class BackupService {
  /// التحقق من إمكانية الوصول للملفات
  static Future<bool> checkAccess() async {
    try {
      // محاولة الوصول للمجلد
      final directory = await getBackupDirectory();
      // التحقق من إمكانية الكتابة في المجلد
      final testFile = File('${directory.path}/test_access.tmp');
      await testFile.writeAsString('test');
      await testFile.delete();
      return true;
    } catch (e) {
      debugPrint('خطأ في الوصول للملفات: $e');
      return false;
    }
  }
  
  /// الحصول على مسار مجلد النسخ الاحتياطية
  static Future<Directory> getBackupDirectory() async {
    if (Platform.isAndroid) {
      // استخدام مجلد التنزيلات لسهولة الوصول إليه
      Directory? directory = await path_provider.getDownloadsDirectory();
      if (directory != null) {
        // إنشاء مجلد خاص بالتطبيق في مجلد التنزيلات
        final appFolder = Directory('${directory.path}/EduTrack');
        if (!await appFolder.exists()) {
          await appFolder.create(recursive: true);
        }
        return appFolder;
      }
    }
    
    // الرجوع إلى مجلد التطبيق الداخلي في حالة الفشل
    return await path_provider.getApplicationDocumentsDirectory();
  }

  /// إنشاء نسخة احتياطية من البيانات
  static Future<String?> createBackup() async {
    try {
      // التحقق من إمكانية الوصول
      bool hasAccess = await checkAccess();
      if (!hasAccess) {
        return null;
      }
      
      // جمع البيانات
      final Map<String, dynamic> backupData = {
        'students': DataService.students.values.map((s) => s.toJson()).toList(),
        'groups': DataService.groups.values.map((g) => g.toJson()).toList(),
        'lessons': DataService.lessons.values.map((l) => l.toJson()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      // تحويل البيانات إلى JSON
      final String jsonData = jsonEncode(backupData);

      // إنشاء اسم الملف
      final String fileName = 'edutrack_backup_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.json';

      // الحصول على مسار المجلد
      final Directory directory = await getBackupDirectory();

      // إنشاء ملف النسخة الاحتياطية
      final File file = File('${directory.path}/$fileName');
      await file.writeAsString(jsonData);

      return file.path;
    } catch (e) {
      debugPrint('خطأ في إنشاء النسخة الاحتياطية: $e');
      return null;
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  static Future<bool> restoreBackup() async {
    try {
      // التحقق من إمكانية الوصول
      bool hasAccess = await checkAccess();
      if (!hasAccess) {
        return false;
      }
      
      // اختيار ملف النسخة الاحتياطية
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) return false;

      // قراءة الملف
      final File file = File(result.files.single.path!);
      return await restoreBackupFromFile(file);
    } catch (e) {
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    }
  }
  
  /// الحصول على وصف مبسط لمكان تخزين النسخ الاحتياطية
  static Future<String> getBackupLocationDescription() async {
    final directory = await getBackupDirectory();
    if (Platform.isAndroid && directory.path.contains('Download')) {
      return 'مجلد التنزيلات/EduTrack:\n${directory.path}\n\nيمكنك الوصول إليه من خلال تطبيق مدير الملفات في هاتفك.';
    } else {
      return 'مجلد التطبيق:\n${directory.path}';
    }
  }
  
  /// فتح مجلد النسخ الاحتياطية
  static Future<bool> openBackupFolder() async {
    try {
      final directory = await getBackupDirectory();
      // إنشاء ملف فارغ للتأكد من وجود المجلد
      final testFile = File('${directory.path}/.folder_exists');
      await testFile.writeAsString('');
      
      // إرجاع مسار المجلد لعرضه للمستخدم
      return true;
    } catch (e) {
      debugPrint('خطأ في فتح مجلد النسخ الاحتياطية: $e');
      return false;
    }
  }
  
  /// الحصول على قائمة النسخ الاحتياطية المتوفرة في مجلد التطبيق
  static Future<List<FileSystemEntity>> getAvailableBackups() async {
    // التحقق من إمكانية الوصول
    bool hasAccess = await checkAccess();
    if (!hasAccess) {
      return [];
    }
    
    final directory = await getBackupDirectory();
    final List<FileSystemEntity> files = directory.listSync();
    return files.where((file) => 
      file is File && 
      path.basename(file.path).startsWith('edutrack_backup_') && 
      path.extension(file.path) == '.json'
    ).toList();
  }
  
  /// استعادة النسخة الاحتياطية من ملف محدد
  static Future<bool> restoreBackupFromFile(File file) async {
    try {
      // التحقق من إمكانية الوصول
      bool hasAccess = await checkAccess();
      if (!hasAccess) {
        return false;
      }
      
      // قراءة الملف
      final String jsonData = await file.readAsString();

      // تحليل البيانات
      final Map<String, dynamic> backupData = jsonDecode(jsonData);

      // حذف البيانات الحالية
      await DataService.students.clear();
      await DataService.groups.clear();
      await DataService.lessons.clear();

      // استعادة المجموعات
      final List<dynamic> groupsData = backupData['groups'];
      for (var groupData in groupsData) {
        final group = GroupJson.fromJson(groupData);
        await DataService.addGroup(group);
      }

      // استعادة الطلاب
      final List<dynamic> studentsData = backupData['students'];
      for (var studentData in studentsData) {
        final student = StudentJson.fromJson(studentData);
        await DataService.addStudent(student);
      }

      // استعادة الدروس
      final List<dynamic> lessonsData = backupData['lessons'];
      for (var lessonData in lessonsData) {
        final lesson = LessonJson.fromJson(lessonData);
        await DataService.addLesson(lesson);
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في استعادة النسخة الاحتياطية: $e');
      return false;
    }
  }
}