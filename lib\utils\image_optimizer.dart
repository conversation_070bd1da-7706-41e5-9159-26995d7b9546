import 'package:flutter/material.dart';
import 'device_utils.dart';
import '../theme/simple_theme.dart';

/// Utility class for optimizing images
class ImageOptimizer {
  /// Singleton instance
  static final ImageOptimizer _instance = ImageOptimizer._internal();

  /// Factory constructor
  factory ImageOptimizer() => _instance;

  /// Internal constructor
  ImageOptimizer._internal() {
    // Initialize with device-specific settings
    _maxCacheSize = DeviceUtils.isLowEndDevice() ? 20 : 50;
  }

  /// Cache for optimized images
  final Map<String, ImageProvider> _imageCache = {};

  /// Maximum number of images to cache - adjusted based on device capability
  late final int _maxCacheSize;

  /// Get or create optimized image
  ImageProvider getOptimizedImage(String key, ImageProvider provider) {
    if (_imageCache.containsKey(key)) {
      return _imageCache[key]!;
    }

    // If cache is full, remove oldest entry
    if (_imageCache.length >= _maxCacheSize) {
      _imageCache.remove(_imageCache.keys.first);
    }

    _imageCache[key] = provider;
    return provider;
  }

  /// Clear image cache
  void clearCache() {
    _imageCache.clear();
  }

  /// Optimize image widget
  Widget optimizedImageWidget({
    required ImageProvider imageProvider,
    String? cacheKey,
    double? width,
    double? height,
    BoxFit? fit,
    Widget? placeholder,
  }) {
    final key = cacheKey ?? imageProvider.hashCode.toString();
    final optimizedProvider = getOptimizedImage(key, imageProvider);

    return Image(
      image: optimizedProvider,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      filterQuality: FilterQuality.medium,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return placeholder ?? const Center(child: CircularProgressIndicator());
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          color: Colors.grey.shade800,
          child: Icon(
            Icons.broken_image,
            color: SimpleTheme.getIconColor(context),
          ),
        );
      },
    );
  }
}
