import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/payment_system_provider.dart';
import '../providers/app_provider.dart';
import '../models/payment_system.dart';
import '../models/student.dart';
import '../models/group.dart';
import '../theme/simple_theme.dart';

class PaymentEntryScreen extends StatefulWidget {
  final String? preSelectedStudentId;

  const PaymentEntryScreen({super.key, this.preSelectedStudentId});

  @override
  State<PaymentEntryScreen> createState() => _PaymentEntryScreenState();
}

class _PaymentEntryScreenState extends State<PaymentEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _receiptController = TextEditingController();

  Student? _selectedStudent;
  Group? _selectedGroup;
  PaymentType _selectedType = PaymentType.monthly;
  PaymentMethod _selectedMethod = PaymentMethod.cash;
  DateTime _dueDate = DateTime.now().add(const Duration(days: 30));
  bool _isPartialPayment = false;
  String? _existingPaymentId;

  @override
  void initState() {
    super.initState();
    final settings = context.read<PaymentSystemProvider>().settings;
    _selectedType = settings.defaultType;
    _selectedMethod = settings.defaultMethod;
    _amountController.text = settings.defaultAmount.toString();

    // إذا تم تمرير معرف طالب مسبقاً
    if (widget.preSelectedStudentId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final appProvider = context.read<AppProvider>();
        final student = appProvider.students.firstWhere(
          (s) => s.id == widget.preSelectedStudentId,
          orElse: () => appProvider.students.first,
        );
        setState(() {
          _selectedStudent = student;
          _selectedGroup = appProvider.groups.firstWhere(
            (g) => g.id == student.groupId,
          );
          _updateAmountFromGroup();
        });
      });
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    _receiptController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          _isPartialPayment ? 'سداد جزئي' : 'إدخال دفعة جديدة',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!_isPartialPayment) ...[
                _buildStudentSelection(),
                const SizedBox(height: 20),
                _buildGroupSelection(),
                const SizedBox(height: 20),
                _buildPaymentTypeSelection(),
                const SizedBox(height: 20),
              ],
              _buildAmountField(),
              const SizedBox(height: 20),
              _buildPaymentMethodSelection(),
              const SizedBox(height: 20),
              if (!_isPartialPayment) ...[
                _buildDueDateField(),
                const SizedBox(height: 20),
              ],
              _buildReceiptField(),
              const SizedBox(height: 20),
              _buildNotesField(),
              const SizedBox(height: 20),
              _buildPartialPaymentOption(),
              const SizedBox(height: 32),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStudentSelection() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الطالب',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: SimpleTheme.getCardColor(context),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: SimpleTheme.getBorderColor(context)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<Student>(
                  value: _selectedStudent,
                  hint: Text(
                    'اختر الطالب',
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getSubtitleColor(context),
                    ),
                  ),
                  isExpanded: true,
                  dropdownColor: SimpleTheme.getCardColor(context),
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                  ),
                  items: appProvider.students.map((student) {
                    return DropdownMenuItem<Student>(
                      value: student,
                      child: Text(student.name),
                    );
                  }).toList(),
                  onChanged: (student) {
                    setState(() {
                      _selectedStudent = student;
                      if (student != null) {
                        _selectedGroup = appProvider.groups.firstWhere(
                          (g) => g.id == student.groupId,
                        );
                        _updateAmountFromGroup();
                        _checkForExistingPayments();
                      }
                    });
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGroupSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المجموعة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: Row(
            children: [
              Icon(Icons.group, color: SimpleTheme.primary, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _selectedGroup?.name ?? 'لم يتم اختيار مجموعة',
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الدفعة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<PaymentType>(
              value: _selectedType,
              isExpanded: true,
              dropdownColor: SimpleTheme.getCardColor(context),
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              items: PaymentType.values.map((type) {
                return DropdownMenuItem<PaymentType>(
                  value: type,
                  child: Text(_getPaymentTypeText(type)),
                );
              }).toList(),
              onChanged: (type) {
                if (type != null) {
                  setState(() => _selectedType = type);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAmountField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المبلغ (ج.م)',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _amountController,
          keyboardType: TextInputType.number,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
          decoration: InputDecoration(
            hintText: _selectedGroup != null && _selectedGroup!.monthlyFee > 0
                ? 'رسوم المجموعة: ${_selectedGroup!.monthlyFee} ج.م'
                : 'أدخل المبلغ',
            hintStyle: GoogleFonts.cairo(
              color: SimpleTheme.getSubtitleColor(context),
            ),
            filled: true,
            fillColor: SimpleTheme.getCardColor(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: SimpleTheme.primary),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'يرجى إدخال المبلغ';
            }
            if (double.tryParse(value) == null) {
              return 'يرجى إدخال رقم صحيح';
            }
            if (double.parse(value) <= 0) {
              return 'يجب أن يكون المبلغ أكبر من صفر';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPaymentMethodSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'وسيلة الدفع',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<PaymentMethod>(
              value: _selectedMethod,
              isExpanded: true,
              dropdownColor: SimpleTheme.getCardColor(context),
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              items: PaymentMethod.values.map((method) {
                return DropdownMenuItem<PaymentMethod>(
                  value: method,
                  child: Row(
                    children: [
                      Icon(
                        _getPaymentMethodIcon(method),
                        color: SimpleTheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(_getPaymentMethodText(method)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (method) {
                if (method != null) {
                  setState(() => _selectedMethod = method);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDueDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ الاستحقاق',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDueDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: SimpleTheme.getCardColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: SimpleTheme.getBorderColor(context)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: SimpleTheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  '${_dueDate.day}/${_dueDate.month}/${_dueDate.year}',
                  style: GoogleFonts.cairo(
                    color: SimpleTheme.getTextColor(context),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReceiptField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'رقم الإيصال (اختياري)',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _receiptController,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
          decoration: InputDecoration(
            hintText: 'رقم الإيصال أو المرجع',
            hintStyle: GoogleFonts.cairo(
              color: SimpleTheme.getSubtitleColor(context),
            ),
            filled: true,
            fillColor: SimpleTheme.getCardColor(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: SimpleTheme.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملاحظات (اختيارية)',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
          decoration: InputDecoration(
            hintText: 'أضف ملاحظات إضافية...',
            hintStyle: GoogleFonts.cairo(
              color: SimpleTheme.getSubtitleColor(context),
            ),
            filled: true,
            fillColor: SimpleTheme.getCardColor(context),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: SimpleTheme.getBorderColor(context),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: SimpleTheme.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPartialPaymentOption() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        if (!paymentProvider.settings.allowPartialPayments) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'سداد جزئي',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      'إضافة دفعة لمبلغ موجود مسبقاً',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _isPartialPayment,
                onChanged: (value) {
                  setState(() {
                    _isPartialPayment = value;
                    if (value) {
                      _checkForExistingPayments();
                    }
                  });
                },
                activeColor: Colors.blue,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _savePayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: SimpleTheme.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          _isPartialPayment ? 'إضافة دفعة جزئية' : 'حفظ الدفعة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
      ),
    );
  }

  void _updateAmountFromGroup() {
    if (_selectedGroup != null && _selectedGroup!.monthlyFee > 0) {
      _amountController.text = _selectedGroup!.monthlyFee.toString();
    }
  }

  void _checkForExistingPayments() {
    if (_selectedStudent != null && _isPartialPayment) {
      final paymentProvider = context.read<PaymentSystemProvider>();
      final existingPayments = paymentProvider
          .getStudentPayments(_selectedStudent!.id)
          .where(
            (p) =>
                p.status == PaymentStatus.pending ||
                p.status == PaymentStatus.partial,
          )
          .toList();

      if (existingPayments.isNotEmpty) {
        // إظهار dialog لاختيار الدفعة المراد إضافة مبلغ لها
        _showExistingPaymentsDialog(existingPayments);
      }
    }
  }

  void _showExistingPaymentsDialog(List<Payment> payments) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        title: Text(
          'اختر الدفعة للسداد الجزئي',
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: payments.map((payment) {
            return ListTile(
              title: Text(
                'دفعة ${_getPaymentTypeText(payment.type)}',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
              subtitle: Text(
                'المبلغ: ${payment.totalAmount} ج.م - المدفوع: ${payment.paidAmount} ج.م',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              onTap: () {
                setState(() {
                  _existingPaymentId = payment.id;
                  _amountController.text = payment.remainingAmount.toString();
                });
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _isPartialPayment = false;
                _existingPaymentId = null;
              });
              Navigator.pop(context);
            },
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDueDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: SimpleTheme.primary,
              surface: SimpleTheme.getCardColor(context),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _dueDate = date);
    }
  }

  void _savePayment() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_isPartialPayment &&
        (_selectedStudent == null || _selectedGroup == null)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'يرجى اختيار الطالب والمجموعة',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final amount = double.parse(_amountController.text);
    final paymentProvider = context.read<PaymentSystemProvider>();

    if (_isPartialPayment && _existingPaymentId != null) {
      // إضافة معاملة دفع جزئي
      await paymentProvider.addPaymentTransaction(
        paymentId: _existingPaymentId!,
        amount: amount,
        method: _selectedMethod,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        receiptNumber: _receiptController.text.isNotEmpty
            ? _receiptController.text
            : null,
      );
    } else {
      // إنشاء دفعة جديدة
      await paymentProvider.createPayment(
        studentId: _selectedStudent!.id,
        groupId: _selectedGroup!.id,
        totalAmount: amount,
        type: _selectedType,
        dueDate: _dueDate,
        method: _selectedMethod,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _isPartialPayment
                ? 'تم إضافة الدفعة الجزئية بنجاح'
                : 'تم إضافة الدفعة بنجاح',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pop(context);
    }
  }

  String _getPaymentTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.monthly:
        return 'شهرية';
      case PaymentType.perSession:
        return 'لكل حصة';
      case PaymentType.perGroup:
        return 'لكل مجموعة';
      case PaymentType.custom:
        return 'مخصصة';
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.mobileWallet:
        return 'محفظة إلكترونية';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.other:
        return 'أخرى';
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileWallet:
        return Icons.phone_android;
      case PaymentMethod.check:
        return Icons.receipt_long;
      case PaymentMethod.other:
        return Icons.more_horiz;
    }
  }
}
