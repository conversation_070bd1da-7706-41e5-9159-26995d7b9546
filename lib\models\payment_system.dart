import 'package:hive/hive.dart';

part 'payment_system.g.dart';

/// نموذج الدفعة
@HiveType(typeId: 10)
class Payment extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String studentId;

  @HiveField(2)
  String groupId;

  @HiveField(3)
  double totalAmount; // المبلغ الإجمالي المطلوب

  @HiveField(4)
  double paidAmount; // المبلغ المدفوع فعلياً

  @HiveField(5)
  PaymentType type;

  @HiveField(6)
  PaymentMethod method;

  @HiveField(7)
  PaymentStatus status;

  @HiveField(8)
  DateTime dueDate; // تاريخ الاستحقاق

  @HiveField(9)
  DateTime? paidDate; // تاريخ الدفع

  @HiveField(10)
  String? notes;

  @HiveField(11)
  DateTime createdAt;

  @HiveField(12)
  DateTime? updatedAt;

  @HiveField(13)
  List<PaymentTransaction> transactions; // سجل المعاملات للسداد الجزئي

  Payment({
    required this.id,
    required this.studentId,
    required this.groupId,
    required this.totalAmount,
    this.paidAmount = 0.0,
    required this.type,
    this.method = PaymentMethod.cash,
    this.status = PaymentStatus.pending,
    required this.dueDate,
    this.paidDate,
    this.notes,
    required this.createdAt,
    this.updatedAt,
    List<PaymentTransaction>? transactions,
  }) : transactions = transactions ?? [];

  /// المبلغ المتبقي
  double get remainingAmount => totalAmount - paidAmount;

  /// هل تم السداد بالكامل؟
  bool get isFullyPaid => paidAmount >= totalAmount;

  /// هل هناك سداد جزئي؟
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < totalAmount;

  /// عدد الأيام المتأخرة
  int get daysOverdue {
    if (status == PaymentStatus.paid) return 0;
    final now = DateTime.now();
    if (now.isAfter(dueDate)) {
      return now.difference(dueDate).inDays;
    }
    return 0;
  }

  /// هل الدفعة متأخرة؟
  bool get isOverdue => daysOverdue > 0 && status != PaymentStatus.paid;

  /// إضافة معاملة دفع جديدة
  void addTransaction(PaymentTransaction transaction) {
    transactions.add(transaction);
    paidAmount += transaction.amount;

    // تحديث الحالة بناءً على المبلغ المدفوع
    if (paidAmount >= totalAmount) {
      status = PaymentStatus.paid;
      paidDate = transaction.date;
    } else if (paidAmount > 0) {
      status = PaymentStatus.partial;
    } else {
      status = PaymentStatus.pending;
    }

    updatedAt = DateTime.now();
    // لا نستدعي save() هنا لتجنب المشاكل - سيتم الحفظ من Provider
  }

  /// تحديث الحالة
  void updateStatus(PaymentStatus newStatus) {
    status = newStatus;
    updatedAt = DateTime.now();
    // لا نستدعي save() هنا لتجنب المشاكل - سيتم الحفظ من Provider
  }
}

/// معاملة دفع (للسداد الجزئي)
@HiveType(typeId: 11)
class PaymentTransaction extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  double amount;

  @HiveField(2)
  PaymentMethod method;

  @HiveField(3)
  DateTime date;

  @HiveField(4)
  String? notes;

  @HiveField(5)
  String? receiptNumber;

  PaymentTransaction({
    required this.id,
    required this.amount,
    required this.method,
    required this.date,
    this.notes,
    this.receiptNumber,
  });
}

/// حالة الدفعة
@HiveType(typeId: 12)
enum PaymentStatus {
  @HiveField(0)
  pending, // معلقة

  @HiveField(1)
  paid, // مدفوعة بالكامل

  @HiveField(2)
  partial, // مدفوعة جزئياً

  @HiveField(3)
  overdue, // متأخرة

  @HiveField(4)
  cancelled, // ملغية
}

/// نوع الدفعة
@HiveType(typeId: 13)
enum PaymentType {
  @HiveField(0)
  monthly, // شهرية

  @HiveField(1)
  perSession, // لكل حصة

  @HiveField(2)
  perGroup, // لكل مجموعة

  @HiveField(3)
  custom, // مخصصة
}

/// وسيلة الدفع
@HiveType(typeId: 14)
enum PaymentMethod {
  @HiveField(0)
  cash, // نقدي

  @HiveField(1)
  bankTransfer, // تحويل بنكي

  @HiveField(2)
  mobileWallet, // محفظة إلكترونية

  @HiveField(3)
  check, // شيك

  @HiveField(4)
  other, // أخرى
}

/// إعدادات نظام الدفع
@HiveType(typeId: 15)
class PaymentSettings extends HiveObject {
  @HiveField(0)
  List<PaymentPolicy> policies; // سياسات تحديد المتأخرين

  @HiveField(1)
  PolicyCombination policyLogic; // منطق دمج السياسات (OR/AND)

  @HiveField(2)
  double defaultAmount; // المبلغ الافتراضي

  @HiveField(3)
  PaymentType defaultType; // نوع الدفع الافتراضي

  @HiveField(4)
  PaymentMethod defaultMethod; // وسيلة الدفع الافتراضية

  @HiveField(5)
  int gracePeriodDays; // فترة السماح بالأيام

  @HiveField(6)
  bool enableNotifications; // تفعيل التنبيهات

  @HiveField(7)
  int notificationDaysBefore; // التنبيه قبل كم يوم

  @HiveField(8)
  bool allowPartialPayments; // السماح بالسداد الجزئي

  @HiveField(9)
  DateTime createdAt;

  @HiveField(10)
  DateTime? updatedAt;

  PaymentSettings({
    List<PaymentPolicy>? policies,
    this.policyLogic = PolicyCombination.or,
    this.defaultAmount = 100.0,
    this.defaultType = PaymentType.monthly,
    this.defaultMethod = PaymentMethod.cash,
    this.gracePeriodDays = 3,
    this.enableNotifications = true,
    this.notificationDaysBefore = 3,
    this.allowPartialPayments = true,
    required this.createdAt,
    this.updatedAt,
  }) : policies = policies ?? [];

  /// إنشاء إعدادات افتراضية
  factory PaymentSettings.defaultSettings() {
    return PaymentSettings(
      createdAt: DateTime.now(),
      policies: [
        PaymentPolicy(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: 'الاستحقاق الشهري',
          type: PolicyType.monthlyDue,
          isEnabled: true,
        ),
      ],
    );
  }

  /// إضافة سياسة جديدة
  void addPolicy(PaymentPolicy policy) {
    policies.add(policy);
    updatedAt = DateTime.now();
    save();
  }

  /// حذف سياسة
  void removePolicy(String policyId) {
    policies.removeWhere((p) => p.id == policyId);
    updatedAt = DateTime.now();
    save();
  }

  /// تحديث الإعدادات
  void updateSettings() {
    updatedAt = DateTime.now();
    save();
  }
}

/// سياسة تحديد المتأخرين عن السداد
@HiveType(typeId: 16)
class PaymentPolicy extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name; // اسم السياسة

  @HiveField(2)
  PolicyType type; // نوع السياسة

  @HiveField(3)
  bool isEnabled; // مفعلة أم لا

  @HiveField(4)
  Map<String, dynamic> parameters; // معاملات السياسة

  PaymentPolicy({
    required this.id,
    required this.name,
    required this.type,
    this.isEnabled = true,
    Map<String, dynamic>? parameters,
  }) : parameters = parameters ?? {};
}

/// نوع سياسة الدفع
@HiveType(typeId: 17)
enum PolicyType {
  @HiveField(0)
  sessionsCompleted, // عدد الحصص المكتملة

  @HiveField(1)
  monthlyDue, // استحقاق شهري

  @HiveField(2)
  lessonCompleted, // انتهاء الدرس الرئيسي

  @HiveField(3)
  custom, // مخصص
}

/// منطق دمج السياسات
@HiveType(typeId: 18)
enum PolicyCombination {
  @HiveField(0)
  or, // أي سياسة تنطبق

  @HiveField(1)
  and, // جميع السياسات تنطبق
}

/// إحصائيات الدفعات
class PaymentStatistics {
  final double totalPaid;
  final double totalPending;
  final double totalOverdue;
  final double totalPartial;
  final int studentsCount;
  final int paidStudentsCount;
  final int overdueStudentsCount;
  final int partialStudentsCount;
  final Map<String, double> revenueByGroup;
  final Map<String, double> revenueByMonth;

  PaymentStatistics({
    required this.totalPaid,
    required this.totalPending,
    required this.totalOverdue,
    required this.totalPartial,
    required this.studentsCount,
    required this.paidStudentsCount,
    required this.overdueStudentsCount,
    required this.partialStudentsCount,
    required this.revenueByGroup,
    required this.revenueByMonth,
  });

  double get totalRevenue => totalPaid + totalPartial;
  double get collectionRate =>
      (totalRevenue / (totalRevenue + totalPending + totalOverdue)) * 100;
}

/// معلومات دفعات الطالب
class StudentPaymentInfo {
  final String studentId;
  final String studentName;
  final String groupName;
  final List<Payment> payments;
  final double totalPaid;
  final double totalPending;
  final double totalOverdue;
  final PaymentStatus overallStatus;
  final List<String> overdueReasons;

  StudentPaymentInfo({
    required this.studentId,
    required this.studentName,
    required this.groupName,
    required this.payments,
    required this.totalPaid,
    required this.totalPending,
    required this.totalOverdue,
    required this.overallStatus,
    required this.overdueReasons,
  });

  bool get hasOverduePayments => totalOverdue > 0;
  bool get hasPartialPayments => payments.any((p) => p.isPartiallyPaid);
}
