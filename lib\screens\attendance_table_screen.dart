import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../models/group.dart';
import '../models/lesson.dart';
import '../theme/simple_theme.dart';
import '../widgets/smooth_animations.dart';
import '../widgets/interactive_widgets.dart';
import 'attendance_history_screen.dart';

class AttendanceTableScreen extends StatefulWidget {
  const AttendanceTableScreen({super.key});

  @override
  State<AttendanceTableScreen> createState() => _AttendanceTableScreenState();
}

class _AttendanceTableScreenState extends State<AttendanceTableScreen> {
  Group? selectedGroup;
  List<DateTime> scheduleDates = [];
  Map<String, Map<String, bool>> attendanceData = {};

  // تحويل رقم اليوم إلى اسم اليوم بالعربية
  final Map<int, String> weekdayNames = {
    DateTime.saturday: 'السبت',
    DateTime.sunday: 'الأحد',
    DateTime.monday: 'الإثنين',
    DateTime.tuesday: 'الثلاثاء',
    DateTime.wednesday: 'الأربعاء',
    DateTime.thursday: 'الخميس',
    DateTime.friday: 'الجمعة',
  };

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final groups = provider.groups;

        return Scaffold(
          backgroundColor: SimpleTheme.darkBg,
          appBar: AppBar(
            backgroundColor: SimpleTheme.darkBg,
            title: Text(
              'جدول الحضور',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: SimpleTheme.getTextColor(context),
              ),
            ),
            centerTitle: true,
            actions: [
              // زر عرض سجل الحضور السابق
              IconButton(
                icon: Icon(
                  Icons.visibility,
                  color: SimpleTheme.getIconColor(context),
                ),
                tooltip: 'عرض سجل الحضور',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AttendanceHistoryScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
          body: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // اختيار المجموعة
                SmoothAnimations.smoothEntry(
                  child: InteractiveCard(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: SimpleTheme.cardBg,
                    child: Container(
                      decoration: BoxDecoration(
                        color: SimpleTheme.getBorderColor(context),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButton<Group>(
                        isExpanded: true,
                        dropdownColor: const Color(0xFF1e293b),
                        underline: const SizedBox(),
                        hint: Text(
                          'اختر المجموعة',
                          style: GoogleFonts.cairo(
                            color: SimpleTheme.getSecondaryTextColor(context),
                          ),
                        ),
                        value: selectedGroup,
                        items: groups.map((group) {
                          return DropdownMenuItem<Group>(
                            value: group,
                            child: Text(
                              group.name,
                              style: GoogleFonts.cairo(
                                color: SimpleTheme.getTextColor(context),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedGroup = value;
                            _initAttendanceData(provider);
                          });
                        },
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // جدول الحضور
                if (selectedGroup != null) ...[
                  Expanded(child: _buildAttendanceTable(provider)),
                ] else ...[
                  Expanded(
                    child: Center(
                      child: Text(
                        'الرجاء اختيار مجموعة لعرض جدول الحضور',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: SimpleTheme.getSecondaryTextColor(context),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  void _initAttendanceData(AppProvider provider) {
    if (selectedGroup == null) return;

    final students = provider.getStudentsByGroup(selectedGroup!.id);
    final groupLessons = provider.lessons
        .where((lesson) => lesson.groupId == selectedGroup!.id)
        .toList();

    // استخراج التواريخ الفريدة من جدول المواعيد
    scheduleDates = [];
    for (final lesson in groupLessons) {
      final lessonDate = DateTime(
        lesson.dateTime.year,
        lesson.dateTime.month,
        lesson.dateTime.day,
      );
      if (!scheduleDates.any(
        (date) =>
            date.year == lessonDate.year &&
            date.month == lessonDate.month &&
            date.day == lessonDate.day,
      )) {
        scheduleDates.add(lessonDate);
      }
    }

    // ترتيب التواريخ
    scheduleDates.sort((a, b) => a.compareTo(b));

    // إذا لم توجد مواعيد، نضيف اليوم الحالي
    if (scheduleDates.isEmpty) {
      scheduleDates.add(DateTime.now());
    }

    // تهيئة بيانات الحضور
    attendanceData.clear();
    for (final student in students) {
      attendanceData[student.id] = {};
      for (final date in scheduleDates) {
        // استخدام التاريخ كمفتاح بتنسيق سنة-شهر-يوم
        final dateKey = '${date.year}-${date.month}-${date.day}';
        attendanceData[student.id]![dateKey] = false;

        // التحقق من الحضور السابق
        for (final lesson in groupLessons) {
          if (lesson.dateTime.year == date.year &&
              lesson.dateTime.month == date.month &&
              lesson.dateTime.day == date.day &&
              lesson.attendedStudentIds.contains(student.id)) {
            attendanceData[student.id]![dateKey] = true;
            break;
          }
        }
      }
    }
  }

  Widget _buildAttendanceTable(AppProvider provider) {
    if (selectedGroup == null) return const SizedBox();

    final students = provider.getStudentsByGroup(selectedGroup!.id);
    if (students.isEmpty) {
      return Center(
        child: Text(
          'لا يوجد طلاب في هذه المجموعة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            color: SimpleTheme.getSecondaryTextColor(context),
          ),
        ),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(
            SimpleTheme.getBorderColor(context),
          ),
          dataRowColor: WidgetStateProperty.all(Colors.transparent),
          border: TableBorder.all(
            color: SimpleTheme.getBorderColor(context),
            borderRadius: BorderRadius.circular(8),
          ),
          columns: [
            DataColumn(
              label: Text(
                'الطالب',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
            ),
            ...scheduleDates.map(
              (date) => DataColumn(
                label: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      weekdayNames[date.weekday] ?? '',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${date.day}/${date.month}/${date.year}',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
          rows: students.map((student) {
            return DataRow(
              cells: [
                DataCell(
                  Text(
                    student.name,
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ),
                ...scheduleDates.map((date) {
                  final dateKey = '${date.year}-${date.month}-${date.day}';
                  return DataCell(
                    Checkbox(
                      value: attendanceData[student.id]?[dateKey] ?? false,
                      onChanged: (value) {
                        setState(() {
                          attendanceData[student.id]![dateKey] = value ?? false;
                          // حفظ البيانات تلقائياً عند تغيير الحالة
                          _saveAttendanceForDate(
                            date,
                            student.id,
                            value ?? false,
                          );
                        });
                      },
                      activeColor: SimpleTheme.primaryBlue,
                      checkColor: SimpleTheme.getTextColor(context),
                    ),
                  );
                }),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  // حفظ بيانات الحضور لطالب محدد في تاريخ محدد
  void _saveAttendanceForDate(DateTime date, String studentId, bool isPresent) {
    if (selectedGroup == null) return;

    final provider = Provider.of<AppProvider>(context, listen: false);

    // البحث عن درس موجود بنفس التاريخ والمجموعة
    Lesson? existingLesson = provider.lessons.firstWhere(
      (lesson) =>
          lesson.groupId == selectedGroup!.id &&
          lesson.dateTime.year == date.year &&
          lesson.dateTime.month == date.month &&
          lesson.dateTime.day == date.day,
      orElse: () => Lesson(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        groupId: selectedGroup!.id,
        dateTime: date,
      ),
    );

    // تحديث قائمة الطلاب الحاضرين
    List<String> attendedStudentIds = List.from(
      existingLesson.attendedStudentIds,
    );

    if (isPresent && !attendedStudentIds.contains(studentId)) {
      attendedStudentIds.add(studentId);
    } else if (!isPresent && attendedStudentIds.contains(studentId)) {
      attendedStudentIds.remove(studentId);
    }

    // تحديث الدرس
    existingLesson.attendedStudentIds = attendedStudentIds;
    existingLesson.isCompleted = true;

    // حفظ الدرس
    try {
      if (existingLesson.isInBox) {
        existingLesson.save();
      } else {
        provider.addLesson(existingLesson);
      }
    } catch (e) {
      // معالجة الخطأ بصمت
      provider.addLesson(existingLesson);
    }
  }

  // This method was removed as it's not being used
}
