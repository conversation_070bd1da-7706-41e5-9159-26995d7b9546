import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// Service for handling file storage operations in the app
class StorageService {
  static const String settingsBox = 'settings';
  static const String cacheBox = 'cache';
  
  /// Initialize storage service
  static Future<void> init() async {
    try {
      // فتح الصناديق بشكل بسيط
      await Hive.openBox(settingsBox);
      await Hive.openBox(cacheBox);
    } catch (e) {
      // تجاهل الأخطاء لتجنب توقف التطبيق
      // Error is ignored to prevent app crash
    }
  }
  
  /// Get the settings box
  static Box get settings => Hive.box(settingsBox);
  
  /// Get the cache box
  static Box get cache => Hive.box(cacheBox);
  
  /// Save a file to the app's documents directory
  static Future<String> saveFile(String fileName, List<int> bytes) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(bytes);
    return file.path;
  }
  
  /// Read a file from the app's documents directory
  static Future<List<int>> readFile(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    return await file.readAsBytes();
  }
  
  /// Delete a file from the app's documents directory
  static Future<void> deleteFile(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    if (await file.exists()) {
      await file.delete();
    }
  }
  
  /// Clear all cached data
  static Future<void> clearCache() async {
    await cache.clear();
  }
}