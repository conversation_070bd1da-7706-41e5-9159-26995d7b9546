import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';

/// خلفية بسيطة وخفيفة للأداء المحسن
class SimpleBackground extends StatelessWidget {
  final Widget child;
  final bool showGradient;
  
  const SimpleBackground({
    super.key,
    required this.child,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // خلفية بسيطة بدون رسوم متحركة معقدة
        gradient: showGradient 
          ? const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                SimpleTheme.darkBg,
                SimpleTheme.cardBg,
              ],
            )
          : null,
        color: showGradient ? null : SimpleTheme.darkBg,
      ),
      child: child,
    );
  }
}

/// خلفية بسيطة للكروت
class SimpleCardBackground extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  
  const SimpleCardBackground({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.cardBg,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        border: Border.all(
          color: SimpleTheme.getBorderColor(context),
          width: 1,
        ),
        // ظل بسيط وخفيف
        boxShadow: const [
          BoxShadow(
            color: Color(0x10000000),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}

/// خلفية بسيطة للأزرار
class SimpleButtonBackground extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final bool isPrimary;
  
  const SimpleButtonBackground({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.isPrimary = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: backgroundColor ?? 
              (isPrimary ? SimpleTheme.primary : SimpleTheme.surfaceBg),
            borderRadius: BorderRadius.circular(8),
            // ظل بسيط
            boxShadow: const [
              BoxShadow(
                color: Color(0x20000000),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: child,
        ),
      ),
    );
  }
}
