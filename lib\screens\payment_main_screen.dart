import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/payment_system_provider.dart';
import '../providers/app_provider.dart';
import '../models/payment_system.dart';
import '../theme/simple_theme.dart';
import 'payment_settings_screen.dart';
import 'payment_entry_screen.dart';
import 'payment_history_screen.dart';
import 'payment_reports_screen.dart';

class PaymentMainScreen extends StatefulWidget {
  const PaymentMainScreen({super.key});

  @override
  State<PaymentMainScreen> createState() => _PaymentMainScreenState();
}

class _PaymentMainScreenState extends State<PaymentMainScreen> {
  String _selectedFilter = 'الكل';
  String _selectedGroup = 'الكل';
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            _buildHeader(),
            _buildFilters(),
            Expanded(child: _buildStudentsList()),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToPaymentEntry(),
        backgroundColor: SimpleTheme.primary,
        icon: Icon(Icons.add, color: SimpleTheme.getIconColor(context)),
        label: Text(
          'إضافة دفعة',
          style: GoogleFonts.cairo(
            color: SimpleTheme.getTextColor(context),
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نظام السداد',
                  style: GoogleFonts.cairo(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                    shadows: [
                      Shadow(
                        color: SimpleTheme.getContainerColor(context),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة مدفوعات الطلاب ومتابعة حالة السداد',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => _navigateToReports(),
                icon: Icon(
                  Icons.analytics,
                  color: SimpleTheme.getIconColor(context),
                  size: 28,
                ),
                tooltip: 'التقارير',
              ),
              IconButton(
                onPressed: () => _navigateToSettings(),
                icon: Icon(
                  Icons.settings,
                  color: SimpleTheme.getIconColor(context),
                  size: 28,
                ),
                tooltip: 'الإعدادات',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: SimpleTheme.getCardColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: SimpleTheme.getBorderColor(context)),
            ),
            child: TextField(
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              decoration: InputDecoration(
                hintText: 'البحث عن طالب...',
                hintStyle: GoogleFonts.cairo(
                  color: SimpleTheme.getSubtitleColor(context),
                ),
                border: InputBorder.none,
                icon: Icon(
                  Icons.search,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
          ),
          const SizedBox(height: 16),

          // فلاتر حالة السداد والمجموعة
          Row(
            children: [
              Expanded(child: _buildStatusFilter()),
              const SizedBox(width: 12),
              Expanded(child: _buildGroupFilter()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Consumer<PaymentSystemProvider>(
      builder: (context, paymentProvider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedFilter,
              hint: Text(
                'حالة السداد',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSubtitleColor(context),
                ),
              ),
              isExpanded: true,
              dropdownColor: SimpleTheme.getCardColor(context),
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              items: ['الكل', 'مدفوع', 'متأخر', 'لم يسدد', 'سداد جزئي'].map((
                status,
              ) {
                return DropdownMenuItem<String>(
                  value: status,
                  child: Text(status),
                );
              }).toList(),
              onChanged: (value) => setState(() => _selectedFilter = value!),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroupFilter() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final groups = ['الكل', ...appProvider.groups.map((g) => g.name)];

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: SimpleTheme.getBorderColor(context)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedGroup,
              hint: Text(
                'المجموعة',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSubtitleColor(context),
                ),
              ),
              isExpanded: true,
              dropdownColor: SimpleTheme.getCardColor(context),
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
              items: groups.map((group) {
                return DropdownMenuItem<String>(
                  value: group,
                  child: Text(group),
                );
              }).toList(),
              onChanged: (value) => setState(() => _selectedGroup = value!),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStudentsList() {
    return Consumer2<AppProvider, PaymentSystemProvider>(
      builder: (context, appProvider, paymentProvider, child) {
        // تطبيق السياسات وتحديد المتأخرين
        final overdueStudents = paymentProvider.getOverdueStudents(
          appProvider.students,
          appProvider.groups,
          appProvider.lessons,
        );

        // إنشاء قائمة معلومات الطلاب
        final allStudentsInfo = <StudentPaymentInfo>[];

        for (final student in appProvider.students) {
          // البحث عن المجموعة بأمان
          final groupIndex = appProvider.groups.indexWhere(
            (g) => g.id == student.groupId,
          );
          if (groupIndex == -1) continue; // تخطي الطالب إذا لم توجد مجموعته

          final group = appProvider.groups[groupIndex];

          // الحصول على معلومات الدفع الأساسية
          var studentInfo = paymentProvider.getStudentPaymentInfo(
            student.id,
            student.name,
            group.name,
          );

          // التحقق من وجود الطالب في قائمة المتأخرين وتحديث المعلومات
          final overdueIndex = overdueStudents.indexWhere(
            (info) => info.studentId == student.id,
          );

          // إذا كان الطالب متأخراً، استخدم معلومات التأخير
          if (overdueIndex != -1) {
            final overdueInfo = overdueStudents[overdueIndex];
            if (overdueInfo.hasOverduePayments &&
                overdueInfo.overdueReasons.isNotEmpty) {
              studentInfo = overdueInfo;
            }
          }

          allStudentsInfo.add(studentInfo);
        }

        // تطبيق الفلاتر
        final filteredStudents = _applyFilters(allStudentsInfo);

        if (filteredStudents.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final studentInfo = filteredStudents[index];
            return _buildStudentCard(studentInfo);
          },
        );
      },
    );
  }

  List<StudentPaymentInfo> _applyFilters(List<StudentPaymentInfo> students) {
    var filtered = students;

    // فلتر البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where(
            (s) =>
                s.studentName.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                s.groupName.toLowerCase().contains(_searchQuery.toLowerCase()),
          )
          .toList();
    }

    // فلتر المجموعة
    if (_selectedGroup != 'الكل') {
      filtered = filtered.where((s) => s.groupName == _selectedGroup).toList();
    }

    // فلتر حالة السداد
    if (_selectedFilter != 'الكل') {
      filtered = filtered.where((s) {
        switch (_selectedFilter) {
          case 'مدفوع':
            return s.overallStatus == PaymentStatus.paid;
          case 'متأخر':
            return s.overallStatus == PaymentStatus.overdue;
          case 'لم يسدد':
            return s.overallStatus == PaymentStatus.pending;
          case 'سداد جزئي':
            return s.overallStatus == PaymentStatus.partial;
          default:
            return true;
        }
      }).toList();
    }

    return filtered;
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.payment_outlined,
            size: 64,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلاتر أو البحث',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentCard(StudentPaymentInfo studentInfo) {
    final statusColor = _getStatusColor(studentInfo.overallStatus);
    final statusText = _getStatusText(studentInfo.overallStatus);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withValues(alpha: 0.2), width: 1),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: statusColor.withValues(alpha: 0.2),
          child: Icon(
            _getStatusIcon(studentInfo.overallStatus),
            color: statusColor,
          ),
        ),
        title: Text(
          studentInfo.studentName,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المجموعة: ${studentInfo.groupName}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
            Text(
              'الحالة: $statusText',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (studentInfo.hasOverduePayments)
              Text(
                'متأخر: ${studentInfo.totalOverdue.toStringAsFixed(0)} ج.م',
                style: GoogleFonts.cairo(fontSize: 12, color: Colors.red),
              ),
          ],
        ),
        trailing: IconButton(
          onPressed: () => _navigateToStudentHistory(studentInfo),
          icon: Icon(Icons.history, color: SimpleTheme.getIconColor(context)),
        ),
      ),
    );
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.partial:
        return Colors.blue;
      case PaymentStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return 'مدفوع';
      case PaymentStatus.pending:
        return 'لم يسدد';
      case PaymentStatus.overdue:
        return 'متأخر';
      case PaymentStatus.partial:
        return 'سداد جزئي';
      case PaymentStatus.cancelled:
        return 'ملغي';
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Icons.check_circle;
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.overdue:
        return Icons.warning;
      case PaymentStatus.partial:
        return Icons.payments;
      case PaymentStatus.cancelled:
        return Icons.cancel;
    }
  }

  void _showStudentDetails(StudentPaymentInfo studentInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        title: Text(
          studentInfo.studentName,
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('المجموعة', studentInfo.groupName),
            _buildDetailRow(
              'المدفوع',
              '${studentInfo.totalPaid.toStringAsFixed(0)} ج.م',
            ),
            _buildDetailRow(
              'المعلق',
              '${studentInfo.totalPending.toStringAsFixed(0)} ج.م',
            ),
            _buildDetailRow(
              'المتأخر',
              '${studentInfo.totalOverdue.toStringAsFixed(0)} ج.م',
            ),
            if (studentInfo.overdueReasons.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'أسباب التأخير:',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getTextColor(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...studentInfo.overdueReasons.map(
                (reason) => Text(
                  '• $reason',
                  style: GoogleFonts.cairo(
                    color: Colors.red.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToPaymentEntry(studentInfo.studentId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: SimpleTheme.primary,
            ),
            child: Text(
              'إضافة دفعة',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.cairo(
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          Text(
            value,
            style: GoogleFonts.cairo(
              color: SimpleTheme.getTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToPaymentEntry([String? studentId]) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PaymentEntryScreen(preSelectedStudentId: studentId),
      ),
    );
  }

  void _navigateToStudentHistory(StudentPaymentInfo studentInfo) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentHistoryScreen(studentInfo: studentInfo),
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PaymentSettingsScreen()),
    );
  }

  void _navigateToReports() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PaymentReportsScreen()),
    );
  }
}
