import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'device_utils.dart';

/// Utility class for memory management
class MemoryUtils {
  static final Map<String, dynamic> _cache = {};
  static Timer? _cleanupTimer;
  static Timer? _gcTimer;
  
  /// Initializes memory management
  static void init() {
    // Set up periodic cache cleanup
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      DeviceUtils.isLowEndDevice() 
          ? const Duration(minutes: 2) // More frequent cleanup on low-end devices
          : const Duration(minutes: 5),
      (_) => cleanCache()
    );
    
    // Set up periodic GC suggestion for low-end devices
    if (DeviceUtils.isLowEndDevice()) {
      _gcTimer?.cancel();
      _gcTimer = Timer.periodic(const Duration(minutes: 5), (_) {
        try {
          // Only run when app is idle
          if (SchedulerBinding.instance.schedulerPhase == SchedulerPhase.idle) {
            PaintingBinding.instance.imageCache.clear();
          }
        } catch (e) {
          // Ignore errors to prevent crashes
        }
      });
    }
  }
  
  /// Stores a value in the memory cache
  static void cacheValue<T>(String key, T value) {
    _cache[key] = _CacheEntry<T>(value);
  }
  
  /// Retrieves a value from the memory cache
  static T? getCachedValue<T>(String key) {
    final entry = _cache[key];
    if (entry != null && entry is _CacheEntry<T>) {
      entry.lastAccessed = DateTime.now();
      return entry.value;
    }
    return null;
  }
  
  /// Cleans up unused cache entries
  static void cleanCache() {
    final now = DateTime.now();
    final keysToRemove = <String>[];
    
    for (final entry in _cache.entries) {
      if (entry.value is _CacheEntry) {
        final cacheEntry = entry.value as _CacheEntry;
        final difference = now.difference(cacheEntry.lastAccessed);
        
        // Remove entries not accessed in the last 10 minutes
        if (difference.inMinutes > 10) {
          keysToRemove.add(entry.key);
        }
      }
    }
    
    for (final key in keysToRemove) {
      _cache.remove(key);
    }
  }
  
  /// Optimizes image caching
  static void optimizeImageCache() {
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50 MB
  }
  
  /// Disposes of resources
  static void dispose() {
    _cleanupTimer?.cancel();
    _gcTimer?.cancel();
    _cleanupTimer = null;
    _gcTimer = null;
    _cache.clear();
  }
}

class _CacheEntry<T> {
  final T value;
  DateTime lastAccessed;
  
  _CacheEntry(this.value) : lastAccessed = DateTime.now();
}