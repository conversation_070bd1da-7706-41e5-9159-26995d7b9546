import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../providers/app_provider.dart';
import '../theme/theme_customization.dart';
import '../theme/simple_theme.dart';
import '../widgets/simple_background.dart';

/// صفحة تخصيص المظهر المتقدم
class ThemeCustomizationScreen extends StatefulWidget {
  const ThemeCustomizationScreen({super.key});

  @override
  State<ThemeCustomizationScreen> createState() =>
      _ThemeCustomizationScreenState();
}

class _ThemeCustomizationScreenState extends State<ThemeCustomizationScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تخصيص المظهر',
          style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        backgroundColor: SimpleTheme.cardBg,
        elevation: 0,
      ),
      body: SimpleBackground(
        child: Consumer<AppProvider>(
          builder: (context, provider, child) {
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // قسم الثيمات الجاهزة
                _buildSectionHeader('الثيمات الجاهزة'),
                _buildPredefinedThemes(provider),

                const SizedBox(height: 24),

                // قسم الألوان المخصصة
                _buildSectionHeader('الألوان المخصصة'),
                _buildCustomColors(provider),

                const SizedBox(height: 24),

                // قسم الخطوط
                _buildSectionHeader('نوع الخط'),
                _buildFontSelection(provider),

                const SizedBox(height: 24),

                // قسم حجم الخط
                _buildSectionHeader('حجم الخط'),
                _buildFontSizeSelection(provider),

                const SizedBox(height: 24),

                // قسم الوضع الليلي التلقائي
                _buildSectionHeader('الوضع الليلي التلقائي'),
                _buildAutoNightMode(provider),

                const SizedBox(height: 32),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: SimpleTheme.primary,
        ),
      ),
    );
  }

  Widget _buildPredefinedThemes(AppProvider provider) {
    return SimpleCardBackground(
      child: Column(
        children: ThemeCustomization.predefinedThemes.entries.map((entry) {
          final isSelected = provider.selectedTheme == entry.key;
          return ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [entry.value['primary']!, entry.value['secondary']!],
                ),
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(color: SimpleTheme.primary, width: 2)
                    : null,
              ),
            ),
            title: Text(
              entry.key,
              style: GoogleFonts.cairo(
                color: SimpleTheme.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
            trailing: isSelected
                ? Icon(
                    Icons.check_circle,
                    color: SimpleTheme.getIconColor(context),
                  )
                : null,
            onTap: () {
              // تطبيق الثيم المختار
              _applyTheme(provider, entry.key, entry.value);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCustomColors(AppProvider provider) {
    return SimpleCardBackground(
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: ThemeCustomization.customColors.entries.map((entry) {
          final isSelected = provider.primaryColor == entry.key;
          return GestureDetector(
            onTap: () {
              // تطبيق اللون المختار
              _applyCustomColor(provider, entry.key, entry.value);
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: entry.value,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                    ? Border.all(
                        color: SimpleTheme.getTextColor(context),
                        width: 3,
                      )
                    : Border.all(
                        color: Colors.white.withValues(alpha: 0.2),
                        width: 1,
                      ),
                boxShadow: [
                  BoxShadow(
                    color: entry.value.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      color: SimpleTheme.getIconColor(context),
                      size: 24,
                    )
                  : null,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFontSelection(AppProvider provider) {
    return SimpleCardBackground(
      child: Column(
        children: ThemeCustomization.arabicFonts.entries.map((entry) {
          final isSelected = provider.fontFamily == entry.key;
          return ListTile(
            title: Text(
              entry.key,
              style: GoogleFonts.getFont(
                entry.value,
                color: SimpleTheme.textPrimary,
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
            subtitle: Text(
              'نموذج للخط العربي الجميل',
              style: GoogleFonts.getFont(
                entry.value,
                color: SimpleTheme.textMuted,
                fontSize: 14,
              ),
            ),
            trailing: isSelected
                ? Icon(
                    Icons.check_circle,
                    color: SimpleTheme.getIconColor(context),
                  )
                : null,
            onTap: () {
              // تطبيق الخط المختار
              _applyFont(provider, entry.key);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFontSizeSelection(AppProvider provider) {
    return SimpleCardBackground(
      child: Column(
        children: ThemeCustomization.fontSizes.entries.map((entry) {
          final isSelected = provider.fontSize == entry.key;
          return ListTile(
            title: Text(
              entry.key,
              style: GoogleFonts.cairo(
                color: SimpleTheme.textPrimary,
                fontSize: 16 * entry.value,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
            subtitle: Text(
              'نموذج للنص بحجم ${entry.key}',
              style: GoogleFonts.cairo(
                color: SimpleTheme.textMuted,
                fontSize: 14 * entry.value,
              ),
            ),
            trailing: isSelected
                ? Icon(
                    Icons.check_circle,
                    color: SimpleTheme.getIconColor(context),
                  )
                : null,
            onTap: () {
              // تطبيق حجم الخط المختار
              _applyFontSize(provider, entry.key);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAutoNightMode(AppProvider provider) {
    return SimpleCardBackground(
      child: Column(
        children: [
          SwitchListTile(
            title: Text(
              'تفعيل الوضع الليلي التلقائي',
              style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
            ),
            subtitle: Text(
              'تبديل تلقائي للوضع الليلي حسب الوقت',
              style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
            ),
            value: provider.autoNightMode,
            onChanged: (value) {
              _toggleAutoNightMode(provider, value);
            },
            activeColor: SimpleTheme.primary,
          ),
          if (provider.autoNightMode) ...[
            const Divider(color: Color(0x20FFFFFF)),
            ListTile(
              title: Text(
                'بداية الوضع الليلي',
                style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
              ),
              subtitle: Text(
                provider.nightModeStart.format(context),
                style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
              ),
              trailing: Icon(
                Icons.access_time,
                color: SimpleTheme.getIconColor(context),
              ),
              onTap: () => _selectTime(context, provider, true),
            ),
            ListTile(
              title: Text(
                'نهاية الوضع الليلي',
                style: GoogleFonts.cairo(color: SimpleTheme.textPrimary),
              ),
              subtitle: Text(
                provider.nightModeEnd.format(context),
                style: GoogleFonts.cairo(color: SimpleTheme.textMuted),
              ),
              trailing: Icon(
                Icons.access_time,
                color: SimpleTheme.getIconColor(context),
              ),
              onTap: () => _selectTime(context, provider, false),
            ),
          ],
        ],
      ),
    );
  }

  void _applyTheme(
    AppProvider provider,
    String themeName,
    Map<String, Color> colors,
  ) {
    // تطبيق الثيم المختار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق ثيم $themeName'),
        backgroundColor: SimpleTheme.primary,
      ),
    );
  }

  void _applyCustomColor(AppProvider provider, String colorName, Color color) {
    // تطبيق اللون المختار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق اللون $colorName'),
        backgroundColor: color,
      ),
    );
  }

  void _applyFont(AppProvider provider, String fontName) {
    // تطبيق الخط المختار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق خط $fontName'),
        backgroundColor: SimpleTheme.primary,
      ),
    );
  }

  void _applyFontSize(AppProvider provider, String sizeName) {
    // تطبيق حجم الخط المختار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تطبيق حجم الخط $sizeName'),
        backgroundColor: SimpleTheme.primary,
      ),
    );
  }

  void _toggleAutoNightMode(AppProvider provider, bool value) {
    // تفعيل/إلغاء الوضع الليلي التلقائي
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          value
              ? 'تم تفعيل الوضع الليلي التلقائي'
              : 'تم إلغاء الوضع الليلي التلقائي',
        ),
        backgroundColor: SimpleTheme.primary,
      ),
    );
  }

  Future<void> _selectTime(
    BuildContext context,
    AppProvider provider,
    bool isStart,
  ) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStart ? provider.nightModeStart : provider.nightModeEnd,
    );

    if (picked != null) {
      // حفظ الوقت المختار - تم حذف SnackBar لتجنب مشكلة BuildContext
    }
  }
}
