name: edu_track
description: "EduTrack - Futuristic Teacher Assistant App"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  provider: ^6.1.1
  connectivity_plus: ^5.0.2
  intl: ^0.19.0
  google_fonts: ^6.1.0
  path_provider: ^2.1.2
  file_picker: ^10.2.0
  path: ^1.8.3
  shared_preferences: ^2.2.2

  # Firebase dependencies
  firebase_core: ^2.24.2
  firebase_remote_config: ^4.3.12
  # Update system dependencies
  package_info_plus: ^8.0.2
  dio: ^5.7.0
  # مكتبات الأمان والحماية
  crypto: ^3.0.3
  device_info_plus: ^10.1.0

  # مكتبات الإشعارات والتذكيرات
  flutter_local_notifications: ^17.2.2
  timezone: ^0.9.4
  permission_handler: ^11.3.1

  # مكتبات التحليلات والرسوم البيانية
  fl_chart: ^0.69.0
  syncfusion_flutter_charts: ^27.1.58


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.3.10
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/

flutter_native_splash:
  color: "#0F172A"
  image: assets/splash_logo.png
  color_dark: "#0F172A"
  image_dark: assets/splash_logo.png
  
  android_12:
    image: assets/splash_logo.png
    icon_background_color: "#0F172A"
    image_dark: assets/splash_logo.png
    icon_background_color_dark: "#0F172A"
  
  fullscreen: true
  android_gravity: center
  ios_content_mode: center
  web_image_mode: center