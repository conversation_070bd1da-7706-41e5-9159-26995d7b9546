import 'package:flutter/material.dart';
import '../utils/device_utils.dart';

/// A ListView optimized for performance with lazy loading
class OptimizedListView<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext, T, int) itemBuilder;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final double? cacheExtent;
  final bool enableLazyLoading;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.cacheExtent,
    this.enableLazyLoading = true,
  });

  @override
  Widget build(BuildContext context) {
    // Determine optimal cache extent based on device capability
    final optimalCacheExtent = cacheExtent ?? 
        (DeviceUtils.isLowEndDevice() ? 100.0 : 200.0);
    
    // For very small lists, use a simple Column instead of ListView for better performance
    if (items.length <= 5 && shrinkWrap) {
      return SingleChildScrollView(
        physics: physics,
        padding: padding,
        controller: controller,
        child: Column(
          children: [
            for (int i = 0; i < items.length; i++)
              RepaintBoundary(
                child: itemBuilder(context, items[i], i),
              ),
          ],
        ),
      );
    }
    
    // For low-end devices with large lists, use more aggressive optimizations
    if (DeviceUtils.isLowEndDevice() && items.length > 20 && enableLazyLoading) {
      return ListView.builder(
        padding: padding,
        physics: physics ?? const ClampingScrollPhysics(),
        shrinkWrap: shrinkWrap,
        controller: controller,
        itemCount: items.length,
        cacheExtent: optimalCacheExtent,
        itemBuilder: (context, index) {
          // Add RepaintBoundary to prevent unnecessary repaints
          return RepaintBoundary(
            child: itemBuilder(context, items[index], index),
          );
        },
        // More aggressive optimizations for low-end devices
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: true,
        addSemanticIndexes: false,
        clipBehavior: Clip.hardEdge,
      );
    }
    
    // Standard optimized ListView for normal cases
    return ListView.builder(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      itemCount: items.length,
      cacheExtent: optimalCacheExtent,
      itemBuilder: (context, index) {
        // Add RepaintBoundary to prevent unnecessary repaints
        return RepaintBoundary(
          child: itemBuilder(context, items[index], index),
        );
      },
      // Optimize scrolling performance
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
    );
  }
}

/// A GridView optimized for performance with lazy loading
class OptimizedGridView<T> extends StatelessWidget {
  final List<T> items;
  final Widget Function(BuildContext, T, int) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final double cacheExtent;

  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.gridDelegate,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.cacheExtent = 200.0,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      gridDelegate: gridDelegate,
      itemCount: items.length,
      cacheExtent: cacheExtent,
      itemBuilder: (context, index) {
        // Add RepaintBoundary to prevent unnecessary repaints
        return RepaintBoundary(
          child: itemBuilder(context, items[index], index),
        );
      },
      // Optimize scrolling performance
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
    );
  }
}