import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../widgets/connection_indicator.dart';
import '../providers/app_provider.dart';
import '../widgets/smooth_animations.dart';
import '../widgets/modern_navigation_bar.dart';
import '../theme/simple_theme.dart';
import 'home_screen.dart';
import 'schedule_screen.dart';
import 'groups_screen.dart';
import 'settings_screen.dart';
import 'students_screen.dart';
import 'payment_main_screen.dart';
import 'attendance_table_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;
  bool _showSecondaryNav = false;

  final List<Widget> _screens = [
    const HomeScreen(),
    const ScheduleScreen(),
    const GroupsScreen(),
    const AttendanceTableScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: Scaffold(
            body: Container(
              decoration: BoxDecoration(
                gradient: SimpleTheme.getBackgroundGradient(context),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Modern top bar with animation - يظهر فقط في الصفحة الرئيسية
                    if (_currentIndex == 0)
                      SmoothAnimations.smoothEntry(
                        child: Container(
                          margin: const EdgeInsets.all(16),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: SimpleTheme.getCardColor(context),
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: SimpleTheme.getBorderColor(context),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              const ConnectionIndicator(),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      Color(0xFF6366f1),
                                      Color(0xFF8B5CF6),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                  borderRadius: BorderRadius.circular(24),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF6366f1,
                                      ).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: Image.asset(
                                        'assets/splash_logo.png',
                                        width: 20,
                                        height: 20,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                              return Icon(
                                                Icons.school_rounded,
                                                color: SimpleTheme.getTextColor(
                                                  context,
                                                ),
                                                size: 20,
                                              );
                                            },
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'EduTrack',
                                      style: GoogleFonts.cairo(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                        color: SimpleTheme.getTextColor(
                                          context,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    // Main content
                    Expanded(
                      child: Stack(
                        children: [
                          AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            transitionBuilder: (child, animation) {
                              return FadeTransition(
                                opacity: animation,
                                child: SlideTransition(
                                  position:
                                      Tween<Offset>(
                                        begin: const Offset(0.1, 0),
                                        end: Offset.zero,
                                      ).animate(
                                        CurvedAnimation(
                                          parent: animation,
                                          curve: Curves.easeInOut,
                                        ),
                                      ),
                                  child: child,
                                ),
                              );
                            },
                            child: _screens[_currentIndex],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: provider.isNavBarAtTop
                ? null
                : _buildModernBottomNavBar(),
            appBar: provider.isNavBarAtTop ? _buildTopNavBar() : null,
            floatingActionButton: _showSecondaryNav
                ? _buildSecondaryNavBar()
                : null,
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
          ),
        );
      },
    );
  }

  // بناء شريط التنقل العلوي
  PreferredSizeWidget _buildTopNavBar() {
    return AppBar(
      backgroundColor: SimpleTheme.getCardColor(context),
      elevation: 0,
      automaticallyImplyLeading: false,
      title: ModernNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          if (index == 4) {
            // زر المزيد
            _toggleSecondaryNav();
          } else {
            setState(() => _currentIndex = index);
          }
        },
        items: const [
          NavigationItem(icon: Icons.home_rounded, label: 'الرئيسية'),
          NavigationItem(icon: Icons.calendar_today_rounded, label: 'الجدول'),
          NavigationItem(icon: Icons.groups_rounded, label: 'المجموعات'),
          NavigationItem(icon: Icons.table_chart_rounded, label: 'الحضور'),
          NavigationItem(icon: Icons.more_horiz_rounded, label: 'المزيد'),
        ],
        isTop: true,
      ),
    );
  }

  // بناء شريط التنقل البسيط
  Widget _buildModernBottomNavBar() {
    return ModernNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        if (index == 4) {
          // زر المزيد
          _toggleSecondaryNav();
        } else {
          setState(() => _currentIndex = index);
        }
      },
      items: const [
        NavigationItem(icon: Icons.home_rounded, label: 'الرئيسية'),
        NavigationItem(icon: Icons.calendar_today_rounded, label: 'الجدول'),
        NavigationItem(icon: Icons.groups_rounded, label: 'المجموعات'),
        NavigationItem(icon: Icons.table_chart_rounded, label: 'الحضور'),
        NavigationItem(icon: Icons.more_horiz_rounded, label: 'المزيد'),
      ],
    );
  }

  // تبديل الشريط الثانوي
  void _toggleSecondaryNav() {
    if (mounted) {
      setState(() {
        _showSecondaryNav = !_showSecondaryNav;
      });
    }
  }

  // بناء الشريط الثانوي البسيط
  Widget _buildSecondaryNavBar() {
    return AnimatedSlide(
      offset: _showSecondaryNav ? Offset.zero : const Offset(0, 1),
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeOutCubic,
      child: AnimatedOpacity(
        opacity: _showSecondaryNav ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          margin: const EdgeInsets.only(bottom: 100),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: SimpleTheme.getBorderColor(context),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSimpleNavItem(
                icon: Icons.people_outline,
                label: 'الطلاب',
                onTap: () => _navigateToStudents(),
              ),
              const SizedBox(width: 12),
              _buildSimpleNavItem(
                icon: Icons.payment,
                label: 'المدفوعات',
                onTap: () => _navigateToPayments(),
              ),
              const SizedBox(width: 12),
              _buildSimpleNavItem(
                icon: Icons.settings,
                label: 'الإعدادات',
                onTap: () => _navigateToSettings(),
              ),
              const SizedBox(width: 12),
              _buildSimpleNavItem(
                icon: Icons.close_rounded,
                label: 'إغلاق',
                onTap: () => _toggleSecondaryNav(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عنصر تنقل بسيط
  Widget _buildSimpleNavItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: SimpleTheme.primary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, size: 18, color: SimpleTheme.primary),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToStudents() {
    _toggleSecondaryNav();
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const StudentsScreen()));
  }

  void _navigateToPayments() {
    _toggleSecondaryNav();
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const PaymentMainScreen()));
  }

  void _navigateToSettings() {
    _toggleSecondaryNav();
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }
}
