import 'package:hive/hive.dart';

part 'group.g.dart';

@HiveType(typeId: 1)
class Group extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String name;

  @HiveField(2)
  String subject;

  @HiveField(3)
  List<String> studentIds;

  @HiveField(4)
  Map<String, List<String>> schedule; // day -> [time1, time2]

  @HiveField(5)
  double monthlyFee;

  @HiveField(6)
  DateTime createdAt;

  Group({
    required this.id,
    required this.name,
    required this.subject,
    List<String>? studentIds,
    Map<String, List<String>>? schedule,
    this.monthlyFee = 0.0,
    DateTime? createdAt,
  }) : studentIds = studentIds ?? [],
       schedule = schedule ?? {},
       createdAt = createdAt ?? DateTime.now();
}