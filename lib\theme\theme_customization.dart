import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// نظام تخصيص المظهر المتقدم
class ThemeCustomization {
  // الألوان المخصصة المتاحة
  static const Map<String, Color> customColors = {
    'أزرق': Color(0xFF2196F3),
    'أخضر': Color(0xFF4CAF50),
    'بنفسجي': Color(0xFF9C27B0),
    'وردي': Color(0xFFE91E63),
    'برتقالي': Color(0xFFFF9800),
    'أحمر': Color(0xFFF44336),
    'تركوازي': Color(0xFF009688),
    'نيلي': Color(0xFF3F51B5),
  };

  // الثيمات الجاهزة
  static const Map<String, Map<String, Color>> predefinedThemes = {
    'أزرق كلاسيكي': {
      'primary': Color(0xFF2196F3),
      'secondary': Color(0xFF03DAC6),
      'accent': Color(0xFFFF9800),
    },
    'أخضر طبيعي': {
      'primary': Color(0xFF4CAF50),
      'secondary': Color(0xFF8BC34A),
      'accent': Color(0xFFFFEB3B),
    },
    'بنفسجي ملكي': {
      'primary': Color(0xFF9C27B0),
      'secondary': Color(0xFFE1BEE7),
      'accent': Color(0xFFFF4081),
    },
    'وردي أنيق': {
      'primary': Color(0xFFE91E63),
      'secondary': Color(0xFFF8BBD9),
      'accent': Color(0xFF673AB7),
    },
  };

  // أحجام الخط المتاحة
  static const Map<String, double> fontSizes = {
    'صغير': 0.8,
    'متوسط': 1.0,
    'كبير': 1.2,
    'كبير جداً': 1.4,
  };

  // الخطوط العربية المتاحة
  static const Map<String, String> arabicFonts = {
    'القاهرة': 'Cairo',
    'عمري': 'Amiri',
    'الرقعة': 'Rakkas',
    'الكوفي': 'Kufam',
    'تاهوما': 'Tajawal',
  };

  /// إنشاء ثيم مخصص
  static ThemeData createCustomTheme({
    required Color primaryColor,
    required Color secondaryColor,
    required Color accentColor,
    required String fontFamily,
    required double fontScale,
    required bool isDark,
  }) {
    final baseTextTheme = isDark
        ? ThemeData.dark().textTheme
        : ThemeData.light().textTheme;

    final customTextTheme = GoogleFonts.getTextTheme(fontFamily, baseTextTheme)
        .apply(
          fontSizeFactor: fontScale,
          bodyColor: isDark ? Colors.white : Colors.black87,
          displayColor: isDark ? Colors.white : Colors.black87,
        );

    return ThemeData(
      brightness: isDark ? Brightness.dark : Brightness.light,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: isDark ? Brightness.dark : Brightness.light,
        secondary: secondaryColor,
      ).copyWith(tertiary: accentColor),
      textTheme: customTextTheme,
      appBarTheme: AppBarTheme(
        backgroundColor: isDark ? const Color(0xFF1A1A1A) : primaryColor,
        foregroundColor: isDark ? Colors.white : Colors.white,
        elevation: 0,
        titleTextStyle: customTextTheme.titleLarge?.copyWith(
          color: isDark ? Colors.white : Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      cardTheme: CardThemeData(
        color: isDark ? const Color(0xFF2A2A2A) : Colors.white,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: isDark ? Colors.white : Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: customTextTheme.labelLarge,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: isDark ? const Color(0xFF1A1A1A) : Colors.white,
        selectedItemColor: primaryColor,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }

  /// إنشاء MaterialColor من Color
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = (color.r * 255.0).round() & 0xff;
    final int g = (color.g * 255.0).round() & 0xff;
    final int b = (color.b * 255.0).round() & 0xff;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.toARGB32(), swatch);
  }

  /// الحصول على ثيم بناءً على الاسم
  static Map<String, Color>? getThemeByName(String themeName) {
    return predefinedThemes[themeName];
  }

  /// الحصول على لون بناءً على الاسم
  static Color? getColorByName(String colorName) {
    return customColors[colorName];
  }

  /// الحصول على حجم الخط بناءً على الاسم
  static double? getFontSizeByName(String sizeName) {
    return fontSizes[sizeName];
  }

  /// الحصول على الخط بناءً على الاسم
  static String? getFontByName(String fontName) {
    return arabicFonts[fontName];
  }

  /// إنشاء تدرج مخصص
  static LinearGradient createCustomGradient(Color primaryColor) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        primaryColor,
        primaryColor.withValues(alpha: 0.8),
        primaryColor.withValues(alpha: 0.6),
      ],
    );
  }

  /// إنشاء ظلال مخصصة
  static List<BoxShadow> createCustomShadow(Color color) {
    return [
      BoxShadow(
        color: color.withValues(alpha: 0.3),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
      BoxShadow(
        color: color.withValues(alpha: 0.1),
        blurRadius: 16,
        offset: const Offset(0, 8),
      ),
    ];
  }

  /// إنشاء ألوان متناسقة
  static Map<String, Color> generateHarmonousColors(Color baseColor) {
    final hsl = HSLColor.fromColor(baseColor);

    return {
      'primary': baseColor,
      'secondary': hsl.withHue((hsl.hue + 120) % 360).toColor(),
      'accent': hsl.withHue((hsl.hue + 240) % 360).toColor(),
      'light': hsl
          .withLightness((hsl.lightness + 0.2).clamp(0.0, 1.0))
          .toColor(),
      'dark': hsl
          .withLightness((hsl.lightness - 0.2).clamp(0.0, 1.0))
          .toColor(),
    };
  }

  /// التحقق من تباين الألوان
  static bool hasGoodContrast(Color foreground, Color background) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();
    final ratio = (luminance1 + 0.05) / (luminance2 + 0.05);
    return ratio >= 4.5; // WCAG AA standard
  }

  /// اقتراح لون نص مناسب
  static Color suggestTextColor(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5
        ? Colors.black87
        : Colors.white;
  }
}
