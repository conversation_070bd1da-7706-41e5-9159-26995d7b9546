package com.example.edu_track

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import android.os.Build
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        flutterEngine.plugins.add(ApkInstallerPlugin())

        // إنشاء قنوات الإشعارات
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // قناة تذكيرات الحصص
            val lessonChannel = NotificationChannel(
                "lesson_reminders",
                "تذكيرات الحصص",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "تذكيرات قبل بداية الحصص"
                enableVibration(true)
                setShowBadge(true)
            }

            // قناة تذكيرات الامتحانات
            val examChannel = NotificationChannel(
                "exam_reminders",
                "تذكيرات الامتحانات",
                NotificationManager.IMPORTANCE_MAX
            ).apply {
                description = "تذكيرات قبل بداية الامتحانات"
                enableVibration(true)
                setShowBadge(true)
            }

            // قناة المهام اليومية
            val taskChannel = NotificationChannel(
                "daily_tasks",
                "المهام اليومية",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "تذكيرات بالمهام اليومية"
                enableVibration(true)
                setShowBadge(true)
            }

            // قناة المنبه الذكي
            val alarmChannel = NotificationChannel(
                "smart_alarm",
                "المنبه الذكي",
                NotificationManager.IMPORTANCE_MAX
            ).apply {
                description = "منبه ذكي حسب الجدول اليومي"
                enableVibration(true)
                setShowBadge(true)
            }

            // قناة الإشعارات الفورية
            val instantChannel = NotificationChannel(
                "instant_notifications",
                "الإشعارات الفورية",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "إشعارات فورية للأحداث المهمة"
                enableVibration(true)
                setShowBadge(true)
            }

            // تسجيل القنوات
            notificationManager.createNotificationChannels(listOf(
                lessonChannel,
                examChannel,
                taskChannel,
                alarmChannel,
                instantChannel
            ))
        }
    }
}
