import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'device_utils.dart';

/// Enhanced Performance Utilities - أدوات تحسين الأداء المحسنة
class PerformanceUtils {
  static bool _isInitialized = false;
  static bool _isLowEndDevice = false;

  /// Initialize all performance optimizations
  static void init() {
    if (_isInitialized) return;

    _isLowEndDevice = DeviceUtils.isLowEndDevice();

    optimizeAnimations();
    optimizeMemory();
    optimizeRendering();

    _isInitialized = true;
  }

  /// Optimizes animations based on device capabilities
  static void optimizeAnimations() {
    if (_isLowEndDevice) {
      // Faster animations for weak devices to reduce load
      timeDilation = 0.7;
    } else {
      // Normal animation speed for powerful devices
      timeDilation = 1.0;
    }
  }

  /// Optimize memory usage for weak devices
  static void optimizeMemory() {
    if (_isLowEndDevice) {
      // Reduce image cache for weak devices
      PaintingBinding.instance.imageCache.maximumSize = 20;
      PaintingBinding.instance.imageCache.maximumSizeBytes =
          10 * 1024 * 1024; // 10MB
    } else {
      // Normal cache for powerful devices
      PaintingBinding.instance.imageCache.maximumSize = 100;
      PaintingBinding.instance.imageCache.maximumSizeBytes =
          50 * 1024 * 1024; // 50MB
    }
  }

  /// Optimize rendering performance
  static void optimizeRendering() {
    if (_isLowEndDevice) {
      // Disable expensive rendering features on weak devices
      SchedulerBinding.instance.addPostFrameCallback((_) {
        // Reduce frame complexity
        SchedulerBinding.instance.resetEpoch();
      });
    }
  }

  /// Creates a widget that avoids unnecessary rebuilds
  static Widget optimizedBuilder({
    required Widget Function(BuildContext) builder,
    List<Object>? dependencies,
  }) {
    return dependencies == null
        ? Builder(builder: builder)
        : Builder(
            builder: (context) {
              for (final dependency in dependencies) {
                if (dependency is Listenable) {
                  return ListenableBuilder(
                    listenable: dependency,
                    builder: (context, _) => builder(context),
                  );
                }
              }
              return builder(context);
            },
          );
  }

  /// Schedules a task to run during idle time
  static void scheduleTask(VoidCallback task) {
    SchedulerBinding.instance.scheduleFrameCallback((_) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        task();
      });
    });
  }

  /// Optimizes image loading and caching
  static Widget optimizedImage(
    ImageProvider imageProvider, {
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Image(
      image: imageProvider,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      filterQuality: FilterQuality.medium,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) {
          return child;
        }
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
          child: child,
        );
      },
    );
  }
}
