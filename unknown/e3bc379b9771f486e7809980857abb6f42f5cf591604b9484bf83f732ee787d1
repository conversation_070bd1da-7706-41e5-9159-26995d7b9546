import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Utility class for device-specific optimizations
class DeviceUtils {
  // Cache the result to avoid repeated calculations
  static bool? _isLowEndDeviceCache;
  
  /// Check if the device is a low-end device
  static bool isLowEndDevice() {
    // Return cached result if available
    if (_isLowEndDeviceCache != null) {
      return _isLowEndDeviceCache!;
    }
    
    // Web platform is considered high-end
    if (kIsWeb) {
      _isLowEndDeviceCache = false;
      return false;
    }
    
    // Check for emulator
    if (Platform.isAndroid && Platform.environment.containsKey('ANDROID_EMULATOR')) {
      _isLowEndDeviceCache = true;
      return true;
    }
    
    // Check device pixel ratio - lower values often indicate lower-end devices
    final pixelRatio = PlatformDispatcher.instance.views.first.devicePixelRatio;
    if (pixelRatio < 2.0) {
      _isLowEndDeviceCache = true;
      return true;
    }
    
    // Check available memory if possible
    try {
      // This is a simple heuristic - devices with less memory are considered low-end
      final screenSize = PlatformDispatcher.instance.views.first.physicalSize;
      final screenArea = screenSize.width * screenSize.height;
      
      // If screen area is small, likely a lower-end device
      if (screenArea < 1000000) { // Roughly equivalent to a 720p screen
        _isLowEndDeviceCache = true;
        return true;
      }
    } catch (e) {
      // Ignore errors in memory detection
    }
    
    _isLowEndDeviceCache = false;
    return false;
  }
  
  /// Get appropriate animation duration based on device capability
  static Duration getAnimationDuration({required Duration normal}) {
    if (isLowEndDevice()) {
      // Reduce animation duration on low-end devices
      return Duration(milliseconds: normal.inMilliseconds ~/ 2);
    }
    return normal;
  }
  
  /// Get appropriate image quality based on device capability
  static FilterQuality getImageQuality() {
    if (isLowEndDevice()) {
      return FilterQuality.low;
    }
    return FilterQuality.medium;
  }
  
  /// Optimize scrolling physics based on device capability
  static ScrollPhysics getScrollPhysics() {
    if (isLowEndDevice()) {
      // Use simpler physics on low-end devices
      return const ClampingScrollPhysics();
    }
    return const BouncingScrollPhysics();
  }
  
  /// Get appropriate cache extent based on device capability
  static double getCacheExtent() {
    if (isLowEndDevice()) {
      return 100.0; // Smaller cache for low-end devices
    }
    return 250.0;
  }
}