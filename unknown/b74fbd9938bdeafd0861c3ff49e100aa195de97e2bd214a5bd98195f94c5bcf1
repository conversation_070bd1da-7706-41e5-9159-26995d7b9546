import 'package:flutter/material.dart';

/// نموذج التذكير
class Reminder {
  final String id;
  final String title;
  final String description;
  final DateTime scheduledTime;
  final ReminderType type;
  final bool isActive;
  final bool isRepeating;
  final RepeatInterval? repeatInterval;
  final Map<String, dynamic>? metadata;

  const Reminder({
    required this.id,
    required this.title,
    required this.description,
    required this.scheduledTime,
    required this.type,
    this.isActive = true,
    this.isRepeating = false,
    this.repeatInterval,
    this.metadata,
  });

  /// إنشاء تذكير من JSON
  factory Reminder.fromJson(Map<String, dynamic> json) {
    return Reminder(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      type: ReminderType.values[json['type'] as int],
      isActive: json['isActive'] as bool? ?? true,
      isRepeating: json['isRepeating'] as bool? ?? false,
      repeatInterval: json['repeatInterval'] != null
          ? RepeatInterval.values[json['repeatInterval'] as int]
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// تحويل التذكير إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'scheduledTime': scheduledTime.toIso8601String(),
      'type': type.index,
      'isActive': isActive,
      'isRepeating': isRepeating,
      'repeatInterval': repeatInterval?.index,
      'metadata': metadata,
    };
  }

  /// نسخ التذكير مع تعديلات
  Reminder copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? scheduledTime,
    ReminderType? type,
    bool? isActive,
    bool? isRepeating,
    RepeatInterval? repeatInterval,
    Map<String, dynamic>? metadata,
  }) {
    return Reminder(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      isRepeating: isRepeating ?? this.isRepeating,
      repeatInterval: repeatInterval ?? this.repeatInterval,
      metadata: metadata ?? this.metadata,
    );
  }

  /// التحقق من انتهاء صلاحية التذكير
  bool get isExpired => scheduledTime.isBefore(DateTime.now());

  /// الحصول على الوقت المتبقي
  Duration get timeRemaining {
    final now = DateTime.now();
    if (scheduledTime.isBefore(now)) {
      return Duration.zero;
    }
    return scheduledTime.difference(now);
  }

  /// تنسيق الوقت المتبقي
  String get formattedTimeRemaining {
    final remaining = timeRemaining;
    if (remaining == Duration.zero) {
      return 'منتهي الصلاحية';
    }

    if (remaining.inDays > 0) {
      return '${remaining.inDays} يوم';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours} ساعة';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes} دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  /// الحصول على أيقونة التذكير
  IconData get icon {
    switch (type) {
      case ReminderType.lesson:
        return Icons.school;
      case ReminderType.exam:
        return Icons.quiz;
      case ReminderType.task:
        return Icons.task_alt;
      case ReminderType.meeting:
        return Icons.meeting_room;
      case ReminderType.deadline:
        return Icons.schedule;
      case ReminderType.custom:
        return Icons.notifications;
    }
  }

  /// الحصول على لون التذكير
  Color get color {
    switch (type) {
      case ReminderType.lesson:
        return Colors.blue;
      case ReminderType.exam:
        return Colors.red;
      case ReminderType.task:
        return Colors.green;
      case ReminderType.meeting:
        return Colors.purple;
      case ReminderType.deadline:
        return Colors.orange;
      case ReminderType.custom:
        return Colors.grey;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Reminder && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Reminder(id: $id, title: $title, type: $type, scheduledTime: $scheduledTime)';
  }
}

/// أنواع التذكيرات
enum ReminderType {
  lesson,    // تذكير بالحصة
  exam,      // تذكير بالامتحان
  task,      // مهمة يومية
  meeting,   // اجتماع
  deadline,  // موعد نهائي
  custom,    // تذكير مخصص
}

/// فترات التكرار
enum RepeatInterval {
  daily,     // يومياً
  weekly,    // أسبوعياً
  monthly,   // شهرياً
  yearly,    // سنوياً
}

/// إعدادات التذكير
class ReminderSettings {
  final bool enableLessonReminders;
  final bool enableExamReminders;
  final bool enableTaskReminders;
  final bool enableSmartAlarm;
  final Duration lessonReminderTime;
  final Duration examReminderTime;
  final Duration smartAlarmBuffer;
  final TimeOfDay dailyTaskTime;

  const ReminderSettings({
    this.enableLessonReminders = true,
    this.enableExamReminders = true,
    this.enableTaskReminders = true,
    this.enableSmartAlarm = false,
    this.lessonReminderTime = const Duration(minutes: 15),
    this.examReminderTime = const Duration(hours: 1),
    this.smartAlarmBuffer = const Duration(hours: 1),
    this.dailyTaskTime = const TimeOfDay(hour: 8, minute: 0),
  });

  /// إنشاء إعدادات من JSON
  factory ReminderSettings.fromJson(Map<String, dynamic> json) {
    return ReminderSettings(
      enableLessonReminders: json['enableLessonReminders'] as bool? ?? true,
      enableExamReminders: json['enableExamReminders'] as bool? ?? true,
      enableTaskReminders: json['enableTaskReminders'] as bool? ?? true,
      enableSmartAlarm: json['enableSmartAlarm'] as bool? ?? false,
      lessonReminderTime: Duration(
        minutes: json['lessonReminderMinutes'] as int? ?? 15,
      ),
      examReminderTime: Duration(
        minutes: json['examReminderMinutes'] as int? ?? 60,
      ),
      smartAlarmBuffer: Duration(
        minutes: json['smartAlarmBufferMinutes'] as int? ?? 60,
      ),
      dailyTaskTime: TimeOfDay(
        hour: json['dailyTaskHour'] as int? ?? 8,
        minute: json['dailyTaskMinute'] as int? ?? 0,
      ),
    );
  }

  /// تحويل الإعدادات إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'enableLessonReminders': enableLessonReminders,
      'enableExamReminders': enableExamReminders,
      'enableTaskReminders': enableTaskReminders,
      'enableSmartAlarm': enableSmartAlarm,
      'lessonReminderMinutes': lessonReminderTime.inMinutes,
      'examReminderMinutes': examReminderTime.inMinutes,
      'smartAlarmBufferMinutes': smartAlarmBuffer.inMinutes,
      'dailyTaskHour': dailyTaskTime.hour,
      'dailyTaskMinute': dailyTaskTime.minute,
    };
  }

  /// نسخ الإعدادات مع تعديلات
  ReminderSettings copyWith({
    bool? enableLessonReminders,
    bool? enableExamReminders,
    bool? enableTaskReminders,
    bool? enableSmartAlarm,
    Duration? lessonReminderTime,
    Duration? examReminderTime,
    Duration? smartAlarmBuffer,
    TimeOfDay? dailyTaskTime,
  }) {
    return ReminderSettings(
      enableLessonReminders: enableLessonReminders ?? this.enableLessonReminders,
      enableExamReminders: enableExamReminders ?? this.enableExamReminders,
      enableTaskReminders: enableTaskReminders ?? this.enableTaskReminders,
      enableSmartAlarm: enableSmartAlarm ?? this.enableSmartAlarm,
      lessonReminderTime: lessonReminderTime ?? this.lessonReminderTime,
      examReminderTime: examReminderTime ?? this.examReminderTime,
      smartAlarmBuffer: smartAlarmBuffer ?? this.smartAlarmBuffer,
      dailyTaskTime: dailyTaskTime ?? this.dailyTaskTime,
    );
  }
}
