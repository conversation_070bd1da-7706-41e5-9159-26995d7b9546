import 'package:flutter/material.dart';
import 'dart:io';

/// أدوات تحسين الأداء للأجهزة المختلفة
class PerformanceUtils {
  static bool _isLowEndDevice = true; // افتراضي للأداء الأمثل
  static bool _initialized = false;
  
  /// تهيئة أدوات الأداء
  static Future<void> initialize() async {
    if (_initialized) return;
    
    _isLowEndDevice = await _detectLowEndDevice();
    _initialized = true;
  }
  
  /// كشف الأجهزة الضعيفة (مبسط للأداء)
  static Future<bool> _detectLowEndDevice() async {
    try {
      // فحص مبسط بناءً على نظام التشغيل
      if (Platform.isAndroid) {
        // افتراض أن الأجهزة القديمة ضعيفة
        return true; // نفترض الأجهزة ضعيفة للأداء الأمثل
      }
    } catch (e) {
      // في حالة فشل الكشف، نفترض أنه جهاز ضعيف للأمان
      return true;
    }
    
    return true; // افتراضي للأداء الأمثل
  }
  
  /// هل الجهاز ضعيف؟
  static bool get isLowEndDevice => _isLowEndDevice;
  
  /// سرعة الرسوم المتحركة المحسنة
  static Duration get animationDuration {
    return _isLowEndDevice 
      ? const Duration(milliseconds: 150)  // سريع جداً للأجهزة الضعيفة
      : const Duration(milliseconds: 250); // سريع للأجهزة القوية
  }
  
  /// حجم الكاش المحسن (مقلل للأداء)
  static int get imageCacheSize {
    return _isLowEndDevice ? 5 * 1024 * 1024 : 20 * 1024 * 1024; // 5MB/20MB
  }
  
  /// عدد العناصر المعروضة في القائمة
  static int get listItemsPerPage {
    return _isLowEndDevice ? 15 : 30;
  }
  
  /// هل نعرض الرسوم المتحركة المعقدة؟
  static bool get showComplexAnimations => false; // مبسط للأداء
  
  /// هل نعرض الظلال المعقدة؟
  static bool get showComplexShadows => false; // مبسط للأداء
  
  /// تحسين الصور للأجهزة الضعيفة
  static double get imageQuality => _isLowEndDevice ? 0.6 : 0.8;
  
  /// تحسين FPS للرسوم المتحركة
  static double get animationScale => _isLowEndDevice ? 0.5 : 0.8;
  
  /// تحسين عدد الألوان في التدرجات
  static int get gradientStops => _isLowEndDevice ? 2 : 3;
  
  /// تحسين نصف قطر الحواف
  static double get borderRadius => _isLowEndDevice ? 8.0 : 12.0;
  
  /// تحسين عدد العناصر في الشبكة
  static int get gridCrossAxisCount => _isLowEndDevice ? 2 : 3;
  
  /// تحسين حجم الخط
  static double optimizeFontSize(double size) {
    return _isLowEndDevice ? size * 0.9 : size;
  }
  
  /// تحسين الحشو
  static EdgeInsets optimizePadding(EdgeInsets padding) {
    return _isLowEndDevice 
      ? EdgeInsets.all(padding.left * 0.8)
      : padding;
  }
  
  /// تحسين الهوامش
  static EdgeInsets optimizeMargin(EdgeInsets margin) {
    return _isLowEndDevice 
      ? EdgeInsets.all(margin.left * 0.8)
      : margin;
  }
  
  /// تحسين الظلال
  static List<BoxShadow> optimizeShadow(List<BoxShadow> shadows) {
    if (_isLowEndDevice) {
      return [
        BoxShadow(
          color: shadows.first.color.withValues(alpha: 0.1),
          blurRadius: shadows.first.blurRadius * 0.5,
          offset: shadows.first.offset * 0.5,
        ),
      ];
    }
    return shadows;
  }
  
  /// تحسين التدرجات
  static LinearGradient optimizeGradient(LinearGradient gradient) {
    if (_isLowEndDevice) {
      return LinearGradient(
        begin: gradient.begin,
        end: gradient.end,
        colors: [
          gradient.colors.first,
          gradient.colors.last,
        ],
      );
    }
    return gradient;
  }
  
  /// تحسين الحدود
  static BorderRadius optimizeBorderRadius(BorderRadius radius) {
    if (_isLowEndDevice) {
      return BorderRadius.circular(borderRadius);
    }
    return radius;
  }
  
  /// تحسين الشفافية
  static double optimizeOpacity(double opacity) {
    return _isLowEndDevice ? opacity * 0.8 : opacity;
  }
  
  /// تحسين سرعة التمرير
  static ScrollPhysics get optimizedScrollPhysics {
    return _isLowEndDevice 
      ? const ClampingScrollPhysics()
      : const BouncingScrollPhysics();
  }
  
  /// تحسين عدد الإطارات في الثانية
  static int get targetFPS => _isLowEndDevice ? 30 : 60;
  
  /// تحسين جودة الرسوم
  static FilterQuality get imageFilterQuality {
    return _isLowEndDevice ? FilterQuality.low : FilterQuality.medium;
  }
}
