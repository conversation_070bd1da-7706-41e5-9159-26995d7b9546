import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';

class ProfileAvatar extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final VoidCallback? onTap;

  const ProfileAvatar({super.key, this.imageUrl, this.size = 80, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: SimpleTheme.getTextColor(context),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: SimpleTheme.getBorderColor(context),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipOval(
          child: imageUrl != null
              ? Image.network(
                  imageUrl!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) =>
                      _buildFallback(context),
                )
              : _buildFallback(context),
        ),
      ),
    );
  }

  Widget _buildFallback(BuildContext context) {
    return Container(
      color: SimpleTheme.getCardColor(context),
      child: Icon(
        Icons.person,
        color: SimpleTheme.getTextColor(context),
        size: size * 0.5,
      ),
    );
  }
}
