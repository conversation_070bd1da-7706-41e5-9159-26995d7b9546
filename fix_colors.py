#!/usr/bin/env python3
"""
Script to fix hardcoded colors in Flutter project
"""

import os
import re
import glob

def fix_colors_in_file(file_path):
    """Fix hardcoded colors in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Fix const SimpleTheme method calls
        content = re.sub(r'const SimpleTheme\.get\w+Color\(context\)', lambda m: m.group(0).replace('const ', ''), content)

        # Fix broken Icon syntax patterns
        content = re.sub(r'Icon\(([^,]+), color: ([^)]+)\)\),\s*size: (\d+),\s*\),', r'Icon(\1, color: \2, size: \3),', content)
        content = re.sub(r'child: Icon\(([^,]+), color: ([^)]+)\)\),', r'child: Icon(\1, color: \2),', content)
        content = re.sub(r'Icon\(([^,]+), color: ([^)]+)\)\),\s*tooltip:', r'Icon(\1, color: \2),\n                tooltip:', content)

        # Fix broken color references with numbers
        content = re.sub(r'SimpleTheme\.getTextColor\(context\)(\d+)', r'SimpleTheme.getSecondaryTextColor(context)', content)
        content = re.sub(r'SimpleTheme\.getTextColor\(context\)24', r'SimpleTheme.getBorderColor(context)', content)
        content = re.sub(r'SimpleTheme\.getTextColor\(context\)70', r'SimpleTheme.getSecondaryTextColor(context)', content)

        # Fix broken ternary operator syntax
        content = re.sub(r'\? Icon\(([^,]+), color: ([^)]+)\), size: (\d+)\)', r'? Icon(\1, color: \2, size: \3)', content)

        # Fix withOpacity to withValues
        content = re.sub(r'\.withOpacity\(([^)]+)\)', r'.withValues(alpha: \1)', content)

        # Fix undefined SimpleTheme references in const contexts
        content = re.sub(r'const TextStyle\(\s*color: SimpleTheme\.getTextColor\(context\),', r'TextStyle(\n                  color: SimpleTheme.getTextColor(context),', content)

        # Fix undefined context in static methods
        if 'simple_theme.dart' in file_path:
            content = re.sub(r'SimpleTheme\.getBorderColor\(context\)', r'const Color(0xFF3A3A3A)', content)
            content = re.sub(r'SimpleTheme\.getTextColor\(context\)', r'Colors.white', content)

        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed syntax in: {file_path}")
            return True

        return False

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix colors in all Dart files"""
    dart_files = glob.glob('lib/**/*.dart', recursive=True)

    fixed_count = 0
    for file_path in dart_files:
        if fix_colors_in_file(file_path):
            fixed_count += 1

    print(f"\nFixed syntax in {fixed_count} files out of {len(dart_files)} total files.")

if __name__ == '__main__':
    main()
