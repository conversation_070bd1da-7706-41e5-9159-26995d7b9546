# 💰 دليل نظام الدفع الشامل - EduTrack

## 🎯 **نظرة عامة**

تم إضافة نظام دفع شامل ومتطور إلى تطبيق EduTrack يوفر:
- **6 طرق مختلفة** لحساب المدفوعات
- **إدارة كاملة** للمدفوعات والمستحقات
- **تنبيهات ذكية** للمدفوعات المتأخرة
- **إحصائيات مفصلة** ومتابعة دقيقة
- **واجهة سهلة** وتصميم أنيق

---

## 🔧 **طرق حساب المدفوعات**

### 1️⃣ **حسب عدد الحصص** (الافتراضية)
- **الآلية**: دفعة كل 8 حصص (قابل للتخصيص)
- **مثال**: إذا حضر الطالب 8 حصص → تُنشأ دفعة تلقائياً
- **الاستخدام**: مناسب للدروس الخصوصية والمجموعات الصغيرة

### 2️⃣ **شهرياً**
- **الآلية**: دفعة في بداية كل شهر
- **مثال**: 1 يناير → دفعة يناير، 1 فبراير → دفعة فبراير
- **الاستخدام**: مناسب للمراكز التعليمية والكورسات الطويلة

### 3️⃣ **عند انتهاء الكورس**
- **الآلية**: دفعة واحدة عند انتهاء جميع دروس المجموعة
- **مثال**: كورس 20 درس → دفعة واحدة في النهاية
- **الاستخدام**: مناسب للكورسات المكثفة والورش

### 4️⃣ **أسبوعياً**
- **الآلية**: دفعة كل أسبوع (ربع المبلغ الشهري)
- **مثال**: 400 ج.م شهرياً → 100 ج.م أسبوعياً
- **الاستخدام**: مناسب للطلاب ذوي الدخل المحدود

### 5️⃣ **كل أسبوعين**
- **الآلية**: دفعة كل أسبوعين (نصف المبلغ الشهري)
- **مثال**: 400 ج.م شهرياً → 200 ج.م كل أسبوعين
- **الاستخدام**: توازن بين المرونة والإدارة

### 6️⃣ **مخصص**
- **الآلية**: إنشاء دفعات يدوياً حسب الحاجة
- **مثال**: دفعات غير منتظمة أو مبالغ متغيرة
- **الاستخدام**: للحالات الخاصة والاتفاقات المرنة

---

## 📱 **كيفية الاستخدام**

### **الوصول لنظام الدفع:**
1. من الصفحة الرئيسية → اضغط "المزيد"
2. اختر "المدفوعات" من القائمة الثانوية
3. أو من الصفحة الرئيسية → شاهد بطاقة "إحصائيات المدفوعات"

### **إعداد النظام لأول مرة:**
1. اذهب إلى **إعدادات الدفع** (أيقونة الترس)
2. اختر **طريقة الحساب** المناسبة
3. حدد **المبلغ الافتراضي** (مثل: 400 ج.م)
4. اضبط **مدة الاستحقاق** (مثل: 30 يوم)
5. فعّل **التنبيهات** إذا رغبت

### **إضافة دفعة جديدة:**
1. اضغط زر **"+"** في صفحة المدفوعات
2. اختر **الطالب** والمجموعة
3. **المبلغ يُملأ تلقائياً** من رسوم المجموعة
4. حدد **نوع الدفعة** (يمكن تعديل المبلغ إذا لزم)
5. اختر **تاريخ الاستحقاق**
6. أضف **ملاحظات** إذا لزم الأمر

---

## � **نظام رسوم المجموعة الذكي**

### **كيف يعمل:**
- **تلقائياً**: يستخدم النظام رسوم المجموعة المحددة مسبقاً
- **ذكياً**: إذا كانت رسوم المجموعة = 0، يستخدم المبلغ الافتراضي
- **مرونة**: يمكن تعديل المبلغ يدوياً عند الحاجة

### **عند إضافة دفعة جديدة:**
1. **اختر المجموعة** → يظهر المبلغ تلقائياً من رسوم المجموعة
2. **إذا لم تكن هناك رسوم محددة** → يستخدم المبلغ الافتراضي من الإعدادات
3. **يمكن تعديل المبلغ** → في أي وقت حسب الحاجة

### **عند إنشاء دفعات تلقائية:**
- **الأولوية الأولى**: رسوم المجموعة
- **الأولوية الثانية**: المبلغ الافتراضي من الإعدادات
- **للدفعات الأسبوعية**: ربع رسوم المجموعة (أو ربع المبلغ الافتراضي)
- **للدفعات نصف الشهرية**: نصف رسوم المجموعة (أو نصف المبلغ الافتراضي)

### **مثال عملي:**
- **مجموعة الرياضيات**: رسوم = 400 ج.م
- **مجموعة الفيزياء**: رسوم = 350 ج.م
- **مجموعة بدون رسوم**: يستخدم المبلغ الافتراضي = 300 ج.م

**النتيجة**: كل مجموعة لها رسومها الخاصة تلقائياً! 🎯

---

## �📊 **الإحصائيات والتقارير**

### **الصفحة الرئيسية:**
- **المدفوع**: إجمالي المبالغ المحصلة
- **المعلق**: المبالغ المستحقة وغير مدفوعة
- **المتأخر**: المبالغ المتأخرة عن موعدها

### **صفحة المدفوعات:**
- **4 تبويبات**: الكل، المدفوع، المعلق، المتأخر
- **بحث وفلترة**: للعثور على دفعات محددة
- **إجراءات سريعة**: تسجيل كمدفوع، تعديل، حذف

### **تفاصيل كل دفعة:**
- اسم الطالب والمجموعة
- المبلغ وتاريخ الاستحقاق
- حالة الدفعة (معلقة، مدفوعة، متأخرة)
- عدد الأيام المتأخرة (إن وجدت)

---

## 🔔 **نظام التنبيهات**

### **أنواع التنبيهات:**
1. **مستحقة قريباً**: قبل 3 أيام من الاستحقاق
2. **متأخرة**: فور تجاوز تاريخ الاستحقاق
3. **متأخرة جداً**: للمدفوعات المتأخرة أكثر من 7 أيام
4. **تقرير يومي**: ملخص المدفوعات المتأخرة
5. **تقرير أسبوعي**: إحصائيات شاملة

### **إعدادات التنبيهات:**
- **تفعيل/إلغاء** التنبيهات
- **عدد الأيام** قبل التنبيه (افتراضي: 3)
- **أيام السماح** قبل اعتبار الدفعة متأخرة

---

## ⚙️ **الإعدادات المتقدمة**

### **إعدادات المبالغ:**
- **المبلغ الافتراضي**: المبلغ المعتاد لكل دفعة
- **عدد الحصص**: للطريقة المبنية على الحصص
- **مدة الاستحقاق**: عدد الأيام المسموحة للدفع

### **إعدادات التوقيت:**
- **أيام السماح**: قبل اعتبار الدفعة متأخرة
- **التنبيه المسبق**: عدد الأيام قبل الاستحقاق

### **إعدادات التشغيل:**
- **إنشاء تلقائي**: إنشاء دفعات تلقائياً حسب الطريقة
- **تفعيل التنبيهات**: تشغيل/إيقاف نظام التنبيهات

---

## 🎯 **أمثلة عملية**

### **مثال 1: مدرس خصوصي**
- **الطريقة**: حسب عدد الحصص (8 حصص)
- **رسوم المجموعة**: 400 ج.م (تُستخدم تلقائياً)
- **النتيجة**: دفعة تلقائية 400 ج.م كل 8 حصص يحضرها الطالب

### **مثال 2: مركز تعليمي**
- **الطريقة**: شهرياً
- **رسوم مجموعة الرياضيات**: 350 ج.م
- **رسوم مجموعة الفيزياء**: 300 ج.م
- **النتيجة**: دفعة شهرية حسب رسوم كل مجموعة

### **مثال 3: كورس مكثف**
- **الطريقة**: عند انتهاء الكورس
- **رسوم المجموعة**: 1000 ج.م للكورس كاملاً
- **النتيجة**: دفعة واحدة 1000 ج.م عند انتهاء جميع الدروس

### **مثال 4: مجموعات متنوعة**
- **مجموعة VIP**: رسوم = 500 ج.م → دفعات 500 ج.م
- **مجموعة عادية**: رسوم = 300 ج.م → دفعات 300 ج.م
- **مجموعة بدون رسوم**: رسوم = 0 → يستخدم المبلغ الافتراضي

---

## 🔍 **نصائح للاستخدام الأمثل**

### **للمدرسين الجدد:**
1. ابدأ بطريقة **"حسب الحصص"** - الأسهل والأوضح
2. حدد مبلغاً **ثابتاً** لتسهيل الحسابات
3. فعّل **التنبيهات** لمتابعة أفضل

### **للمراكز الكبيرة:**
1. استخدم الطريقة **"الشهرية"** للانتظام
2. اضبط **أيام السماح** حسب سياسة المركز
3. راجع **التقارير الأسبوعية** بانتظام

### **للكورسات المتنوعة:**
1. استخدم الطريقة **"المخصصة"** للمرونة
2. أضف **ملاحظات مفصلة** لكل دفعة
3. راقب **المدفوعات المتأخرة** باستمرار

---

## 🚀 **المميزات المتقدمة**

### **الحساب التلقائي:**
- تتبع حضور الطلاب تلقائياً
- إنشاء دفعات عند الوصول للعدد المطلوب
- تحديث حالة المدفوعات المتأخرة

### **التكامل مع النظام:**
- ربط المدفوعات بالطلاب والمجموعات
- عرض الإحصائيات في الصفحة الرئيسية
- تنبيهات ذكية ومتنوعة

### **المرونة والتخصيص:**
- 6 طرق مختلفة للحساب
- إعدادات قابلة للتخصيص بالكامل
- واجهة سهلة ومتجاوبة

---

## 🎉 **الخلاصة**

نظام الدفع في EduTrack يوفر:
- ✅ **سهولة الاستخدام** مع قوة الميزات
- ✅ **مرونة كاملة** في طرق الحساب
- ✅ **متابعة دقيقة** للمدفوعات والمستحقات
- ✅ **تنبيهات ذكية** لعدم نسيان أي دفعة
- ✅ **إحصائيات شاملة** لاتخاذ قرارات مدروسة

**نظام متكامل لإدارة مالية احترافية!** 💰✨
