import 'package:flutter/material.dart';
import '../theme/simple_theme.dart';

/// Smooth & Lightweight Animations - رسوم متحركة سلسة وخفيفة
class SmoothAnimations {
  // Animation durations optimized for weak devices
  static const Duration ultraFast = Duration(milliseconds: 100);
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 200);
  static const Duration slow = Duration(milliseconds: 300);
  
  // Optimized curves for smooth performance
  static const Curve smoothCurve = Curves.easeOutCubic;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve slideCurve = Curves.easeOutQuart;
  
  /// Fade In Animation - رسم متحرك للظهور التدريجي
  static Widget fadeIn({
    required Widget child,
    Duration? duration,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = smoothCurve,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? (PerformanceHelper.isLowEndDevice() ? fast : normal),
      tween: Tween(begin: begin, end: end),
      curve: curve,
      builder: (context, value, child) => Opacity(
        opacity: value,
        child: child,
      ),
      child: child,
    );
  }
  
  /// Scale Animation - رسم متحرك للتكبير والتصغير
  static Widget scaleIn({
    required Widget child,
    Duration? duration,
    double begin = 0.8,
    double end = 1.0,
    Curve curve = bounceCurve,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? (PerformanceHelper.isLowEndDevice() ? fast : normal),
      tween: Tween(begin: begin, end: end),
      curve: curve,
      builder: (context, value, child) => Transform.scale(
        scale: value,
        child: child,
      ),
      child: child,
    );
  }
  
  /// Slide Animation - رسم متحرك للانزلاق
  static Widget slideIn({
    required Widget child,
    Duration? duration,
    Offset begin = const Offset(0, 0.3),
    Offset end = Offset.zero,
    Curve curve = slideCurve,
  }) {
    return TweenAnimationBuilder<Offset>(
      duration: duration ?? (PerformanceHelper.isLowEndDevice() ? fast : normal),
      tween: Tween(begin: begin, end: end),
      curve: curve,
      builder: (context, value, child) => Transform.translate(
        offset: Offset(value.dx * 50, value.dy * 50),
        child: child,
      ),
      child: child,
    );
  }
  
  /// Rotation Animation - رسم متحرك للدوران
  static Widget rotateIn({
    required Widget child,
    Duration? duration,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = smoothCurve,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration ?? normal,
      tween: Tween(begin: begin, end: end),
      curve: curve,
      builder: (context, value, child) => Transform.rotate(
        angle: value * 0.1, // Subtle rotation
        child: child,
      ),
      child: child,
    );
  }
  
  /// Combined Animation - رسم متحرك مركب
  static Widget smoothEntry({
    required Widget child,
    Duration? duration,
    double fadeBegin = 0.0,
    double scaleBegin = 0.9,
    Offset slideBegin = const Offset(0, 0.2),
  }) {
    final animDuration = duration ?? (PerformanceHelper.isLowEndDevice() ? fast : normal);
    
    return fadeIn(
      duration: animDuration,
      begin: fadeBegin,
      child: scaleIn(
        duration: animDuration,
        begin: scaleBegin,
        child: slideIn(
          duration: animDuration,
          begin: slideBegin,
          child: child,
        ),
      ),
    );
  }
}

/// Interactive Animations - رسوم متحركة تفاعلية
class InteractiveAnimations extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final double scaleDown;
  final Duration duration;
  final bool enableHaptic;
  
  const InteractiveAnimations({
    super.key,
    required this.child,
    this.onTap,
    this.scaleDown = 0.95,
    this.duration = const Duration(milliseconds: 100),
    this.enableHaptic = true,
  });
  
  @override
  State<InteractiveAnimations> createState() => _InteractiveAnimationsState();
}

class _InteractiveAnimationsState extends State<InteractiveAnimations>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleDown,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }
  
  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    if (widget.onTap != null) {
      widget.onTap!();
    }
  }
  
  void _onTapCancel() {
    _controller.reverse();
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) => Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.child,
        ),
      ),
    );
  }
}

/// Staggered Animation - رسم متحرك متدرج
class StaggeredAnimation extends StatelessWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration duration;
  final Axis direction;
  
  const StaggeredAnimation({
    super.key,
    required this.children,
    this.delay = const Duration(milliseconds: 50),
    this.duration = const Duration(milliseconds: 200),
    this.direction = Axis.vertical,
  });
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return SmoothAnimations.smoothEntry(
          duration: Duration(
            milliseconds: duration.inMilliseconds + (delay.inMilliseconds * index),
          ),
          slideBegin: direction == Axis.vertical 
            ? Offset(0, 0.3) 
            : Offset(0.3, 0),
          child: child,
        );
      }).toList(),
    );
  }
}

/// Floating Action Button with Animation - زر عائم مع رسوم متحركة
class AnimatedFloatingButton extends StatefulWidget {
  final VoidCallback onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  
  const AnimatedFloatingButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
  });
  
  @override
  State<AnimatedFloatingButton> createState() => _AnimatedFloatingButtonState();
}

class _AnimatedFloatingButtonState extends State<AnimatedFloatingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    // Start animation after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) _controller.forward();
    });
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) => Transform.scale(
        scale: _scaleAnimation.value,
        child: Transform.rotate(
          angle: _rotationAnimation.value * 0.1,
          child: FloatingActionButton(
            onPressed: widget.onPressed,
            backgroundColor: widget.backgroundColor ?? SimpleTheme.primary,
            tooltip: widget.tooltip,
            child: Icon(
              widget.icon,
              color: SimpleTheme.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}
