import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme/simple_theme.dart';

import 'enhanced_card.dart';
import 'animated_counter.dart';

class StatsDashboard extends StatefulWidget {
  final List<StatItem> stats;
  final int crossAxisCount;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final double spacing;

  const StatsDashboard({
    super.key,
    required this.stats,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.2,
    this.padding,
    this.spacing = 16,
  });

  @override
  State<StatsDashboard> createState() => _StatsDashboardState();
}

class _StatsDashboardState extends State<StatsDashboard>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.stats.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 800 + (index * 200)),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.elasticOut));
    }).toList();

    // Start animations with staggered delay
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 150), () {
        if (!_isDisposed && mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? EdgeInsets.zero,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          crossAxisSpacing: widget.spacing,
          mainAxisSpacing: widget.spacing,
          childAspectRatio: widget.childAspectRatio,
        ),
        itemCount: widget.stats.length,
        itemBuilder: (context, index) {
          final stat = widget.stats[index];
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Transform.scale(
                scale: _animations[index].value,
                child: Transform.translate(
                  offset: Offset(0, (1 - _animations[index].value) * 50),
                  child: Opacity(
                    opacity: (_animations[index].value).clamp(0.0, 1.0),
                    child: _buildStatCard(stat, index),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildStatCard(StatItem stat, int index) {
    return EnhancedCard(
      onTap: stat.onTap,
      type: CardType.premium,
      enableHoverEffect: true,
      enablePressEffect: true,
      padding: const EdgeInsets.all(16),
      animationDuration: const Duration(milliseconds: 250),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Enhanced Icon Container
          Hero(
            tag: 'stat_icon_$index',
            child: Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    stat.color.withValues(alpha: 0.2),
                    stat.color.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: stat.color.withValues(alpha: 0.4),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: stat.color.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: -1,
                  ),
                ],
              ),
              child: Icon(stat.icon, color: stat.color, size: 22),
            ),
          ),
          const SizedBox(height: 8),
          // Enhanced Counter
          Hero(
            tag: 'stat_value_$index',
            child: Material(
              color: Colors.transparent,
              child: ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: [SimpleTheme.textPrimary, SimpleTheme.textSecondary],
                ).createShader(bounds),
                child: AnimatedCounter(
                  value: stat.value,
                  style: GoogleFonts.cairo(
                    fontSize: 22,
                    fontWeight: FontWeight.w900,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 6),
          // Enhanced Title
          Flexible(
            child: Text(
              stat.title,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: SimpleTheme.textSecondary,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          const SizedBox(height: 4),
          // Progress Indicator
          if (stat.progress != null) ...[
            const SizedBox(height: 4),
            LinearProgressIndicator(
              value: (stat.progress! / 100).clamp(0.0, 1.0),
              backgroundColor: stat.color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(stat.color),
              borderRadius: BorderRadius.circular(2),
            ),
            const SizedBox(height: 4),
            Text(
              '${stat.progress!.isNaN ? 0 : stat.progress!.toInt()}%',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: SimpleTheme.textMuted,
                fontWeight: FontWeight.w600,
              ),
            ),
          ] else ...[
            // Subtle indicator
            Container(
              width: 30,
              height: 3,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    stat.color.withValues(alpha: 0.6),
                    stat.color.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class StatItem {
  final String title;
  final int value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final double? progress;
  final String? subtitle;

  const StatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.progress,
    this.subtitle,
  });
}

// Quick Stats Widget for common use cases - Simple Design
class QuickStats extends StatelessWidget {
  final int totalGroups;
  final int totalStudents;
  final int completedLessons;
  final int remainingLessons;
  final VoidCallback? onGroupsTap;
  final VoidCallback? onStudentsTap;
  final VoidCallback? onCompletedTap;
  final VoidCallback? onRemainingTap;

  const QuickStats({
    super.key,
    required this.totalGroups,
    required this.totalStudents,
    required this.completedLessons,
    required this.remainingLessons,
    this.onGroupsTap,
    this.onStudentsTap,
    this.onCompletedTap,
    this.onRemainingTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // الصف الأول - المجموعات والطلاب
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: SimpleTheme.getBorderColor(context),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildSimpleStatCard(
                  context,
                  'المجموعات',
                  totalGroups.toString(),
                  Icons.groups_rounded,
                  SimpleTheme.primary,
                  onGroupsTap,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: SimpleTheme.getBorderColor(context),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              Expanded(
                child: _buildSimpleStatCard(
                  context,
                  'الطلاب',
                  totalStudents.toString(),
                  Icons.person_rounded,
                  SimpleTheme.accentGreen,
                  onStudentsTap,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // الصف الثاني - الدروس المكتملة والمتبقية
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: SimpleTheme.getCardColor(context),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: SimpleTheme.getBorderColor(context),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildSimpleStatCard(
                  context,
                  'المكتملة',
                  completedLessons.toString(),
                  Icons.check_circle_rounded,
                  SimpleTheme.accentGreen,
                  onCompletedTap,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: SimpleTheme.getBorderColor(context),
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              Expanded(
                child: _buildSimpleStatCard(
                  context,
                  'المتبقية',
                  remainingLessons.toString(),
                  Icons.schedule_rounded,
                  SimpleTheme.accentOrange,
                  onRemainingTap,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 8),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getSecondaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
