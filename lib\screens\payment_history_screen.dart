import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/payment_system_provider.dart';
import '../models/payment_system.dart';
import '../theme/simple_theme.dart';
import 'payment_entry_screen.dart';

class PaymentHistoryScreen extends StatefulWidget {
  final StudentPaymentInfo studentInfo;

  const PaymentHistoryScreen({super.key, required this.studentInfo});

  @override
  State<PaymentHistoryScreen> createState() => _PaymentHistoryScreenState();
}

class _PaymentHistoryScreenState extends State<PaymentHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: SimpleTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'سجل مدفوعات ${widget.studentInfo.studentName}',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: SimpleTheme.getIconColor(context),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            onPressed: () => _navigateToAddPayment(),
            icon: Icon(Icons.add, color: SimpleTheme.getIconColor(context)),
            tooltip: 'إضافة دفعة',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSummaryCard(),
          _buildTabBar(),
          Expanded(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SimpleTheme.primary.withValues(alpha: 0.1),
            SimpleTheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: SimpleTheme.primary.withValues(alpha: 0.2),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: SimpleTheme.primary.withValues(alpha: 0.2),
                child: Icon(Icons.person, color: SimpleTheme.primary),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.studentInfo.studentName,
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                    Text(
                      'المجموعة: ${widget.studentInfo.groupName}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'المدفوع',
                  '${widget.studentInfo.totalPaid.toStringAsFixed(0)} ج.م',
                  Colors.green,
                  Icons.check_circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryItem(
                  'المعلق',
                  '${widget.studentInfo.totalPending.toStringAsFixed(0)} ج.م',
                  Colors.orange,
                  Icons.pending,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryItem(
                  'المتأخر',
                  '${widget.studentInfo.totalOverdue.toStringAsFixed(0)} ج.م',
                  Colors.red,
                  Icons.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: SimpleTheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
        labelColor: SimpleTheme.getTextColor(context),
        unselectedLabelColor: SimpleTheme.getSecondaryTextColor(context),
        labelStyle: GoogleFonts.cairo(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'جميع الدفعات'),
          Tab(text: 'المعاملات'),
          Tab(text: 'الملاحظات'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPaymentsList(),
        _buildTransactionsList(),
        _buildNotesList(),
      ],
    );
  }

  Widget _buildPaymentsList() {
    final payments = widget.studentInfo.payments;

    if (payments.isEmpty) {
      return _buildEmptyState(
        'لا توجد دفعات',
        'لم يتم إنشاء أي دفعات لهذا الطالب بعد',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        return _buildPaymentCard(payment);
      },
    );
  }

  Widget _buildTransactionsList() {
    final allTransactions = <PaymentTransaction>[];
    for (final payment in widget.studentInfo.payments) {
      allTransactions.addAll(payment.transactions);
    }

    allTransactions.sort((a, b) => b.date.compareTo(a.date));

    if (allTransactions.isEmpty) {
      return _buildEmptyState(
        'لا توجد معاملات',
        'لم يتم تسجيل أي معاملات دفع بعد',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: allTransactions.length,
      itemBuilder: (context, index) {
        final transaction = allTransactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  Widget _buildNotesList() {
    final paymentsWithNotes = widget.studentInfo.payments
        .where((p) => p.notes != null && p.notes!.isNotEmpty)
        .toList();

    if (paymentsWithNotes.isEmpty) {
      return _buildEmptyState(
        'لا توجد ملاحظات',
        'لم يتم إضافة أي ملاحظات للدفعات',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: paymentsWithNotes.length,
      itemBuilder: (context, index) {
        final payment = paymentsWithNotes[index];
        return _buildNoteCard(payment);
      },
    );
  }

  Widget _buildPaymentCard(Payment payment) {
    final statusColor = _getStatusColor(payment.status);
    final statusText = _getStatusText(payment.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: statusColor.withValues(alpha: 0.2), width: 1),
      ),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: statusColor.withValues(alpha: 0.2),
          child: Icon(
            _getStatusIcon(payment.status),
            color: statusColor,
            size: 20,
          ),
        ),
        title: Text(
          'دفعة ${_getPaymentTypeText(payment.type)}',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: SimpleTheme.getTextColor(context),
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المبلغ: ${payment.totalAmount.toStringAsFixed(0)} ج.م',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
            ),
            Text(
              'الحالة: $statusText',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: SimpleTheme.getIconColor(context)),
          color: SimpleTheme.getCardColor(context),
          onSelected: (value) => _handlePaymentAction(value, payment),
          itemBuilder: (context) => [
            if (payment.status != PaymentStatus.paid)
              PopupMenuItem(
                value: 'add_payment',
                child: Row(
                  children: [
                    const Icon(Icons.add, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'إضافة دفعة',
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            if (payment.status != PaymentStatus.paid)
              PopupMenuItem(
                value: 'mark_paid',
                child: Row(
                  children: [
                    const Icon(Icons.check, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'تسجيل كمدفوع',
                      style: GoogleFonts.cairo(
                        color: SimpleTheme.getTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            PopupMenuItem(
              value: 'cancel',
              child: Row(
                children: [
                  const Icon(Icons.cancel, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow(
                  'المبلغ الإجمالي',
                  '${payment.totalAmount.toStringAsFixed(0)} ج.م',
                ),
                _buildDetailRow(
                  'المبلغ المدفوع',
                  '${payment.paidAmount.toStringAsFixed(0)} ج.م',
                ),
                _buildDetailRow(
                  'المبلغ المتبقي',
                  '${payment.remainingAmount.toStringAsFixed(0)} ج.م',
                ),
                _buildDetailRow(
                  'تاريخ الاستحقاق',
                  _formatDate(payment.dueDate),
                ),
                if (payment.paidDate != null)
                  _buildDetailRow(
                    'تاريخ الدفع',
                    _formatDate(payment.paidDate!),
                  ),
                _buildDetailRow(
                  'وسيلة الدفع',
                  _getPaymentMethodText(payment.method),
                ),
                if (payment.notes != null && payment.notes!.isNotEmpty)
                  _buildDetailRow('الملاحظات', payment.notes!),
                if (payment.transactions.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'المعاملات (${payment.transactions.length}):',
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...payment.transactions.map(
                    (transaction) => _buildTransactionSummary(transaction),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(PaymentTransaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Colors.green.withValues(alpha: 0.2),
            child: Icon(
              _getPaymentMethodIcon(transaction.method),
              color: Colors.green,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${transaction.amount.toStringAsFixed(0)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  _getPaymentMethodText(transaction.method),
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                ),
                Text(
                  _formatDate(transaction.date),
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: SimpleTheme.getSubtitleColor(context),
                  ),
                ),
                if (transaction.receiptNumber != null)
                  Text(
                    'إيصال: ${transaction.receiptNumber}',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: SimpleTheme.getSubtitleColor(context),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoteCard(Payment payment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                'دفعة ${_getPaymentTypeText(payment.type)}',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: SimpleTheme.getTextColor(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            payment.notes!,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _formatDate(payment.createdAt),
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: SimpleTheme.getSubtitleColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionSummary(PaymentTransaction transaction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${transaction.amount.toStringAsFixed(0)} ج.م',
            style: GoogleFonts.cairo(
              color: Colors.green,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            _formatDate(transaction.date),
            style: GoogleFonts.cairo(
              color: SimpleTheme.getSecondaryTextColor(context),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getSecondaryTextColor(context),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.4),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Colors.green;
      case PaymentStatus.pending:
        return Colors.orange;
      case PaymentStatus.overdue:
        return Colors.red;
      case PaymentStatus.partial:
        return Colors.blue;
      case PaymentStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getStatusText(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return 'مدفوع';
      case PaymentStatus.pending:
        return 'معلق';
      case PaymentStatus.overdue:
        return 'متأخر';
      case PaymentStatus.partial:
        return 'جزئي';
      case PaymentStatus.cancelled:
        return 'ملغي';
    }
  }

  IconData _getStatusIcon(PaymentStatus status) {
    switch (status) {
      case PaymentStatus.paid:
        return Icons.check_circle;
      case PaymentStatus.pending:
        return Icons.pending;
      case PaymentStatus.overdue:
        return Icons.warning;
      case PaymentStatus.partial:
        return Icons.payments;
      case PaymentStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getPaymentTypeText(PaymentType type) {
    switch (type) {
      case PaymentType.monthly:
        return 'شهرية';
      case PaymentType.perSession:
        return 'لكل حصة';
      case PaymentType.perGroup:
        return 'لكل مجموعة';
      case PaymentType.custom:
        return 'مخصصة';
    }
  }

  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
      case PaymentMethod.mobileWallet:
        return 'محفظة إلكترونية';
      case PaymentMethod.check:
        return 'شيك';
      case PaymentMethod.other:
        return 'أخرى';
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return Icons.money;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileWallet:
        return Icons.phone_android;
      case PaymentMethod.check:
        return Icons.receipt_long;
      case PaymentMethod.other:
        return Icons.more_horiz;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _handlePaymentAction(String action, Payment payment) {
    final paymentProvider = context.read<PaymentSystemProvider>();

    switch (action) {
      case 'add_payment':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentEntryScreen(
              preSelectedStudentId: widget.studentInfo.studentId,
            ),
          ),
        );
        break;
      case 'mark_paid':
        paymentProvider.markPaymentAsPaid(paymentId: payment.id);
        setState(() {});
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تسجيل الدفعة كمدفوعة',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
        break;
      case 'cancel':
        _showCancelDialog(payment);
        break;
    }
  }

  void _showCancelDialog(Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: SimpleTheme.getCardColor(context),
        title: Text(
          'إلغاء الدفعة',
          style: GoogleFonts.cairo(color: SimpleTheme.getTextColor(context)),
        ),
        content: Text(
          'هل أنت متأكد من إلغاء هذه الدفعة؟',
          style: GoogleFonts.cairo(color: Colors.white.withValues(alpha: 0.8)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'تراجع',
              style: GoogleFonts.cairo(
                color: SimpleTheme.getTextColor(context),
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              context.read<PaymentSystemProvider>().cancelPayment(payment.id);
              setState(() {});
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم إلغاء الدفعة', style: GoogleFonts.cairo()),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: Text(
              'إلغاء الدفعة',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToAddPayment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentEntryScreen(
          preSelectedStudentId: widget.studentInfo.studentId,
        ),
      ),
    );
  }
}
